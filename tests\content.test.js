const request = require('supertest');
const mongoose = require('mongoose');
const app = require('../server/server');
const { User, ContentSession } = require('../server/models');

describe('Content Routes', () => {
  let server, testUser, authToken;

  beforeAll(async () => {
    const mongoUri = process.env.MONGODB_TEST_URI || 'mongodb://localhost:27017/brainripe_test';
    await mongoose.connect(mongoUri);
    server = app.listen(0);
  });

  afterAll(async () => {
    await mongoose.connection.dropDatabase();
    await mongoose.connection.close();
    server.close();
  });

  beforeEach(async () => {
    await User.deleteMany({});
    await ContentSession.deleteMany({});

    // Create test user and get auth token
    testUser = new User({
      username: 'testuser',
      password: 'password123',
      dateOfBirth: new Date('2010-01-01'),
      interests: ['science', 'technology']
    });
    await testUser.save();

    const loginResponse = await request(app)
      .post('/api/auth/login')
      .send({
        username: 'testuser',
        password: 'password123'
      });

    authToken = loginResponse.body.token;
  });

  describe('GET /api/content/curate', () => {
    it('should get curated content for user', async () => {
      const response = await request(app)
        .get('/api/content/curate')
        .set('Authorization', `Bearer ${authToken}`)
        .expect(200);

      expect(response.body.success).toBe(true);
      expect(response.body.content).toBeDefined();
      expect(response.body.userLevel).toBe(testUser.currentLevel);
      expect(response.body.chunkSizeLevel).toBe(testUser.chunkSizeLevel);
    });

    it('should filter content by type when specified', async () => {
      const response = await request(app)
        .get('/api/content/curate?contentType=article')
        .set('Authorization', `Bearer ${authToken}`)
        .expect(200);

      expect(response.body.success).toBe(true);
      expect(response.body.content.articles).toBeDefined();
      expect(response.body.content.videos).toBeUndefined();
      expect(response.body.content.books).toBeUndefined();
    });

    it('should require authentication', async () => {
      const response = await request(app)
        .get('/api/content/curate')
        .expect(401);

      expect(response.body.code).toBe('NO_TOKEN');
    });
  });

  describe('POST /api/content/start-session', () => {
    it('should start a new content session', async () => {
      const sessionData = {
        contentUrl: 'https://example.com/article',
        contentTitle: 'Test Article',
        contentType: 'article',
        contentSource: 'Test Source',
        estimatedDuration: 10,
        content: 'This is a test article content.'
      };

      const response = await request(app)
        .post('/api/content/start-session')
        .set('Authorization', `Bearer ${authToken}`)
        .send(sessionData)
        .expect(201);

      expect(response.body.success).toBe(true);
      expect(response.body.session.contentTitle).toBe('Test Article');
      expect(response.body.session.contentType).toBe('article');
      expect(response.body.session.totalChunks).toBeGreaterThan(0);
      expect(response.body.chunkedContent).toBeDefined();
    });

    it('should resume existing session for same content', async () => {
      const sessionData = {
        contentUrl: 'https://example.com/article',
        contentTitle: 'Test Article',
        contentType: 'article'
      };

      // Start first session
      await request(app)
        .post('/api/content/start-session')
        .set('Authorization', `Bearer ${authToken}`)
        .send(sessionData)
        .expect(201);

      // Try to start same session again
      const response = await request(app)
        .post('/api/content/start-session')
        .set('Authorization', `Bearer ${authToken}`)
        .send(sessionData)
        .expect(200);

      expect(response.body.success).toBe(true);
      expect(response.body.message).toBe('Resuming existing session');
    });

    it('should reject invalid content type', async () => {
      const sessionData = {
        contentUrl: 'https://example.com/content',
        contentTitle: 'Test Content',
        contentType: 'invalid-type'
      };

      const response = await request(app)
        .post('/api/content/start-session')
        .set('Authorization', `Bearer ${authToken}`)
        .send(sessionData)
        .expect(400);

      expect(response.body.code).toBe('INVALID_CONTENT_TYPE');
    });

    it('should require authentication', async () => {
      const sessionData = {
        contentUrl: 'https://example.com/article',
        contentTitle: 'Test Article',
        contentType: 'article'
      };

      const response = await request(app)
        .post('/api/content/start-session')
        .send(sessionData)
        .expect(401);

      expect(response.body.code).toBe('NO_TOKEN');
    });
  });

  describe('POST /api/content/complete-chunk/:sessionId', () => {
    let testSession;

    beforeEach(async () => {
      // Create a test session
      testSession = new ContentSession({
        userId: testUser._id,
        contentUrl: 'https://example.com/article',
        contentTitle: 'Test Article',
        contentType: 'article',
        totalChunks: 3,
        completedChunks: 0,
        chunkDetails: [],
        status: 'active'
      });
      await testSession.save();
    });

    it('should complete a chunk successfully', async () => {
      const response = await request(app)
        .post(`/api/content/complete-chunk/${testSession._id}`)
        .set('Authorization', `Bearer ${authToken}`)
        .send({
          chunkIndex: 0,
          timeSpent: 300 // 5 minutes
        })
        .expect(200);

      expect(response.body.success).toBe(true);
      expect(response.body.chunkResult).toBeDefined();
      expect(response.body.session.completedChunks).toBe(1);
      expect(response.body.user.totalPoints).toBeGreaterThan(testUser.totalPoints);
    });

    it('should reject invalid chunk index', async () => {
      const response = await request(app)
        .post(`/api/content/complete-chunk/${testSession._id}`)
        .set('Authorization', `Bearer ${authToken}`)
        .send({
          chunkIndex: 10, // Invalid index
          timeSpent: 300
        })
        .expect(400);

      expect(response.body.code).toBe('INVALID_CHUNK_INDEX');
    });

    it('should reject completion for non-existent session', async () => {
      const fakeSessionId = new mongoose.Types.ObjectId();
      
      const response = await request(app)
        .post(`/api/content/complete-chunk/${fakeSessionId}`)
        .set('Authorization', `Bearer ${authToken}`)
        .send({
          chunkIndex: 0,
          timeSpent: 300
        })
        .expect(404);

      expect(response.body.code).toBe('SESSION_NOT_FOUND');
    });
  });

  describe('GET /api/content/active-sessions', () => {
    beforeEach(async () => {
      // Create test sessions
      const session1 = new ContentSession({
        userId: testUser._id,
        contentUrl: 'https://example.com/article1',
        contentTitle: 'Test Article 1',
        contentType: 'article',
        totalChunks: 3,
        completedChunks: 1,
        status: 'active'
      });

      const session2 = new ContentSession({
        userId: testUser._id,
        contentUrl: 'https://example.com/video1',
        contentTitle: 'Test Video 1',
        contentType: 'video',
        totalChunks: 2,
        completedChunks: 0,
        status: 'paused'
      });

      await session1.save();
      await session2.save();
    });

    it('should get user active sessions', async () => {
      const response = await request(app)
        .get('/api/content/active-sessions')
        .set('Authorization', `Bearer ${authToken}`)
        .expect(200);

      expect(response.body.success).toBe(true);
      expect(response.body.sessions).toHaveLength(2);
      expect(response.body.sessions[0].contentTitle).toBeDefined();
      expect(response.body.sessions[0].completionPercentage).toBeDefined();
    });

    it('should require authentication', async () => {
      const response = await request(app)
        .get('/api/content/active-sessions')
        .expect(401);

      expect(response.body.code).toBe('NO_TOKEN');
    });
  });

  describe('GET /api/content/completed-sessions', () => {
    beforeEach(async () => {
      // Create completed session
      const completedSession = new ContentSession({
        userId: testUser._id,
        contentUrl: 'https://example.com/completed',
        contentTitle: 'Completed Article',
        contentType: 'article',
        totalChunks: 2,
        completedChunks: 2,
        status: 'completed',
        completedAt: new Date(),
        pointsEarned: 20,
        totalTimeSpent: 600
      });

      await completedSession.save();
    });

    it('should get user completed sessions', async () => {
      const response = await request(app)
        .get('/api/content/completed-sessions')
        .set('Authorization', `Bearer ${authToken}`)
        .expect(200);

      expect(response.body.success).toBe(true);
      expect(response.body.sessions).toHaveLength(1);
      expect(response.body.sessions[0].contentTitle).toBe('Completed Article');
      expect(response.body.sessions[0].pointsEarned).toBe(20);
    });

    it('should limit results when specified', async () => {
      const response = await request(app)
        .get('/api/content/completed-sessions?limit=5')
        .set('Authorization', `Bearer ${authToken}`)
        .expect(200);

      expect(response.body.success).toBe(true);
      expect(response.body.sessions.length).toBeLessThanOrEqual(5);
    });
  });

  describe('POST /api/content/pause-session/:sessionId', () => {
    let activeSession;

    beforeEach(async () => {
      activeSession = new ContentSession({
        userId: testUser._id,
        contentUrl: 'https://example.com/article',
        contentTitle: 'Test Article',
        contentType: 'article',
        totalChunks: 3,
        completedChunks: 1,
        status: 'active'
      });
      await activeSession.save();
    });

    it('should pause active session', async () => {
      const response = await request(app)
        .post(`/api/content/pause-session/${activeSession._id}`)
        .set('Authorization', `Bearer ${authToken}`)
        .expect(200);

      expect(response.body.success).toBe(true);
      expect(response.body.session.status).toBe('paused');
    });

    it('should reject pausing non-existent session', async () => {
      const fakeSessionId = new mongoose.Types.ObjectId();
      
      const response = await request(app)
        .post(`/api/content/pause-session/${fakeSessionId}`)
        .set('Authorization', `Bearer ${authToken}`)
        .expect(404);

      expect(response.body.code).toBe('SESSION_NOT_FOUND');
    });
  });

  describe('POST /api/content/resume-session/:sessionId', () => {
    let pausedSession;

    beforeEach(async () => {
      pausedSession = new ContentSession({
        userId: testUser._id,
        contentUrl: 'https://example.com/article',
        contentTitle: 'Test Article',
        contentType: 'article',
        totalChunks: 3,
        completedChunks: 1,
        status: 'paused'
      });
      await pausedSession.save();
    });

    it('should resume paused session', async () => {
      const response = await request(app)
        .post(`/api/content/resume-session/${pausedSession._id}`)
        .set('Authorization', `Bearer ${authToken}`)
        .expect(200);

      expect(response.body.success).toBe(true);
      expect(response.body.session.status).toBe('active');
    });
  });

  describe('POST /api/content/rate-session/:sessionId', () => {
    let completedSession;

    beforeEach(async () => {
      completedSession = new ContentSession({
        userId: testUser._id,
        contentUrl: 'https://example.com/article',
        contentTitle: 'Test Article',
        contentType: 'article',
        totalChunks: 2,
        completedChunks: 2,
        status: 'completed',
        completedAt: new Date()
      });
      await completedSession.save();
    });

    it('should rate completed session', async () => {
      const response = await request(app)
        .post(`/api/content/rate-session/${completedSession._id}`)
        .set('Authorization', `Bearer ${authToken}`)
        .send({
          rating: 5,
          feedback: 'Great content!'
        })
        .expect(200);

      expect(response.body.success).toBe(true);
      expect(response.body.message).toBe('Session rated successfully');
    });

    it('should reject invalid rating', async () => {
      const response = await request(app)
        .post(`/api/content/rate-session/${completedSession._id}`)
        .set('Authorization', `Bearer ${authToken}`)
        .send({
          rating: 6 // Invalid rating
        })
        .expect(400);

      expect(response.body.code).toBe('INVALID_RATING');
    });
  });
});
