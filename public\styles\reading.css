/* Reading Interface Styles */

#reading-screen {
  background: linear-gradient(135deg, #ffecd2 0%, #fcb69f 100%);
  min-height: 100vh;
}

.reading-container {
  max-width: 800px;
  margin: 0 auto;
  padding: 0;
}

/* Reading Header */
.reading-header {
  background: white;
  padding: 1rem 2rem;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.1);
  display: flex;
  align-items: center;
  justify-content: space-between;
  position: sticky;
  top: 0;
  z-index: 100;
}

.reading-title {
  flex: 1;
  text-align: center;
  margin: 0 2rem;
}

.reading-title h2 {
  margin: 0;
  color: #2d3748;
  font-size: 1.5rem;
}

.reading-title p {
  margin: 0;
  color: #718096;
  font-size: 0.9rem;
}

.reading-controls {
  display: flex;
  gap: 0.5rem;
}

/* Progress Container */
.progress-container {
  background: white;
  padding: 1.5rem 2rem;
  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.05);
}

.progress-container .progress-bar {
  height: 12px;
  margin-bottom: 1rem;
  background: #e2e8f0;
  border-radius: 6px;
  overflow: hidden;
  position: relative;
}

.progress-container .progress-fill {
  background: linear-gradient(90deg, #4facfe 0%, #00f2fe 100%);
  height: 100%;
  border-radius: 6px;
  transition: width 0.8s ease;
  position: relative;
}

.progress-container .progress-fill::after {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.4), transparent);
  animation: progressShimmer 2s infinite;
}

@keyframes progressShimmer {
  0% { transform: translateX(-100%); }
  100% { transform: translateX(100%); }
}

.progress-text {
  text-align: center;
  font-weight: 600;
  color: #4a5568;
  font-size: 1.1rem;
}

/* Chunk Container */
.chunk-container {
  padding: 2rem;
}

.chunk-content {
  background: white;
  border-radius: 25px;
  padding: 2.5rem;
  margin-bottom: 2rem;
  box-shadow: 0 15px 40px rgba(0, 0, 0, 0.1);
  line-height: 1.8;
  font-size: 1.1rem;
  color: #2d3748;
}

/* Chunk Header */
.chunk-header {
  margin-bottom: 2rem;
  padding-bottom: 1rem;
  border-bottom: 2px solid #e2e8f0;
}

.chunk-header h3 {
  margin-bottom: 0.5rem;
  color: #2d3748;
  display: flex;
  align-items: center;
  gap: 0.5rem;
}

.chunk-meta {
  display: flex;
  gap: 1rem;
  font-size: 0.9rem;
  color: #718096;
}

.reading-time,
.watch-time {
  display: flex;
  align-items: center;
  gap: 0.5rem;
}

/* Chunk Text */
.chunk-text {
  font-size: 1.1rem;
  line-height: 1.8;
  color: #2d3748;
}

.chunk-text p {
  margin-bottom: 1.5rem;
}

.chunk-text p:last-child {
  margin-bottom: 0;
}

/* Video Container */
.video-container {
  text-align: center;
}

.video-instructions {
  background: linear-gradient(135deg, #a8edea 0%, #fed6e3 100%);
  padding: 2rem;
  border-radius: 20px;
  margin-bottom: 1.5rem;
}

.video-instructions p {
  margin-bottom: 1.5rem;
  font-size: 1.1rem;
  color: #2d3748;
}

.video-link {
  display: inline-flex;
  align-items: center;
  gap: 0.5rem;
  text-decoration: none;
}

.video-tips {
  background: #f7fafc;
  padding: 1.5rem;
  border-radius: 15px;
  border-left: 4px solid #4facfe;
}

.video-tips p {
  margin: 0;
  color: #4a5568;
}

/* Chunk Footer */
.chunk-footer {
  margin-top: 2rem;
  padding-top: 1.5rem;
  border-top: 2px solid #e2e8f0;
}

.reading-tips {
  background: linear-gradient(135deg, #ffeaa7 0%, #fab1a0 100%);
  padding: 1.5rem;
  border-radius: 15px;
  text-align: center;
}

.reading-tips p {
  margin: 0;
  color: #2d3748;
  font-size: 0.95rem;
}

/* Chunk Controls */
.chunk-controls {
  text-align: center;
  padding: 0 2rem 2rem;
}

.chunk-controls .btn-large {
  font-size: 1.2rem;
  padding: 1.25rem 2.5rem;
  min-width: 300px;
  position: relative;
  overflow: hidden;
}

.chunk-controls .btn-large::before {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
  transition: left 0.5s ease;
}

.chunk-controls .btn-large:hover::before {
  left: 100%;
}

#chunk-points {
  background: rgba(255, 255, 255, 0.2);
  padding: 0.25rem 0.75rem;
  border-radius: 15px;
  font-size: 0.9rem;
  margin-left: 0.5rem;
}

/* Completion Modal */
.completion-modal .modal-content {
  max-width: 600px;
  text-align: center;
}

.celebration {
  background: linear-gradient(135deg, #ffeaa7 0%, #fab1a0 100%);
  color: #2d3748;
  border-radius: 25px;
}

.celebration-icon {
  font-size: 4rem;
  margin-bottom: 1rem;
  animation: celebrationBounce 1s ease-in-out infinite;
}

@keyframes celebrationBounce {
  0%, 100% { transform: scale(1); }
  50% { transform: scale(1.1); }
}

.celebration h3 {
  font-size: 2rem;
  margin-bottom: 1rem;
  color: #2d3748;
}

.celebration p {
  font-size: 1.2rem;
  margin-bottom: 2rem;
  color: #4a5568;
}

.completion-stats {
  display: flex;
  justify-content: center;
  gap: 2rem;
  margin-bottom: 2rem;
}

.completion-stat {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  font-size: 1.1rem;
  font-weight: 600;
  color: #2d3748;
}

.completion-stat .stat-icon {
  font-size: 1.5rem;
}

.completion-actions {
  display: flex;
  gap: 1rem;
  justify-content: center;
  flex-wrap: wrap;
}

/* Badge Animation */
.badge-animation {
  position: absolute;
  top: 30%;
  left: 50%;
  transform: translate(-50%, -50%);
  animation: badgeSlideIn 3s ease-out forwards;
}

.badge-content {
  text-align: center;
  background: linear-gradient(135deg, #ffd89b 0%, #19547b 100%);
  color: white;
  padding: 2rem;
  border-radius: 20px;
  box-shadow: 0 20px 60px rgba(0, 0, 0, 0.3);
}

.badge-icon {
  font-size: 3rem;
  margin-bottom: 0.5rem;
}

.badge-text {
  font-size: 1.2rem;
  font-weight: bold;
  margin-bottom: 0.5rem;
}

.badge-name {
  font-size: 1.5rem;
  font-weight: bold;
}

@keyframes badgeSlideIn {
  0% {
    opacity: 0;
    transform: translate(-50%, -50%) scale(0.5) rotate(-10deg);
  }
  20% {
    opacity: 1;
    transform: translate(-50%, -50%) scale(1.1) rotate(5deg);
  }
  40% {
    transform: translate(-50%, -50%) scale(0.95) rotate(-2deg);
  }
  60% {
    transform: translate(-50%, -50%) scale(1.05) rotate(1deg);
  }
  80% {
    transform: translate(-50%, -50%) scale(1) rotate(0deg);
  }
  100% {
    opacity: 0;
    transform: translate(-50%, -50%) scale(1) rotate(0deg);
  }
}

/* Responsive Design */
@media (max-width: 768px) {
  .reading-header {
    padding: 1rem;
    flex-direction: column;
    gap: 1rem;
  }
  
  .reading-title {
    margin: 0;
  }
  
  .reading-title h2 {
    font-size: 1.25rem;
  }
  
  .progress-container {
    padding: 1rem;
  }
  
  .chunk-container {
    padding: 1rem;
  }
  
  .chunk-content {
    padding: 1.5rem;
    font-size: 1rem;
  }
  
  .chunk-controls .btn-large {
    min-width: auto;
    width: 100%;
    font-size: 1rem;
    padding: 1rem 1.5rem;
  }
  
  .completion-stats {
    flex-direction: column;
    gap: 1rem;
  }
  
  .completion-actions {
    flex-direction: column;
  }
}

@media (max-width: 480px) {
  .reading-header {
    padding: 0.75rem;
  }
  
  .reading-title h2 {
    font-size: 1.1rem;
  }
  
  .chunk-content {
    padding: 1rem;
    font-size: 0.95rem;
    line-height: 1.6;
  }
  
  .chunk-header h3 {
    font-size: 1.1rem;
  }
  
  .chunk-meta {
    flex-direction: column;
    gap: 0.5rem;
  }
  
  .celebration h3 {
    font-size: 1.5rem;
  }
  
  .celebration p {
    font-size: 1rem;
  }
  
  .completion-stat {
    font-size: 1rem;
  }
}

/* Print Styles */
@media print {
  .reading-header,
  .progress-container,
  .chunk-controls,
  .chunk-footer {
    display: none;
  }
  
  .chunk-content {
    box-shadow: none;
    border: 1px solid #ccc;
  }
}
