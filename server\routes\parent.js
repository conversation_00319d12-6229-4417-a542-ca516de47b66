const express = require('express');
const { User, ParentAccount, ContentSession } = require('../models');
const { authenticateParent } = require('../middleware/auth');

const router = express.Router();

// Helper function to get week start
function getWeekStart() {
  const now = new Date();
  const dayOfWeek = now.getDay();
  const diff = now.getDate() - dayOfWeek;
  return new Date(now.setDate(diff));
}

// Get child's dashboard data for parent
router.get('/child-dashboard', authenticateParent, async (req, res) => {
  try {
    const childUser = req.childUser;
    const parentAccount = req.parentAccount;
    
    // Get child's basic stats
    const stats = await ContentSession.getUserStats(childUser._id);
    
    // Get this week's data
    const weekStart = getWeekStart();
    const weekEnd = new Date(weekStart);
    weekEnd.setDate(weekStart.getDate() + 7);
    
    const thisWeekSessions = await ContentSession.find({
      userId: childUser._id,
      createdAt: { $gte: weekStart, $lt: weekEnd }
    });
    
    // Get this month's data
    const monthStart = new Date();
    monthStart.setDate(1);
    monthStart.setHours(0, 0, 0, 0);
    
    const thisMonthSessions = await ContentSession.find({
      userId: childUser._id,
      createdAt: { $gte: monthStart }
    });
    
    // Calculate content type breakdown
    const contentTypeBreakdown = await ContentSession.aggregate([
      { $match: { userId: childUser._id, status: 'completed' } },
      {
        $group: {
          _id: '$contentType',
          count: { $sum: 1 },
          totalTime: { $sum: '$totalTimeSpent' }
        }
      }
    ]);
    
    // Calculate reading streak
    const readingStreak = await calculateReadingStreak(childUser._id);
    
    // Get today's screen time
    const today = new Date();
    today.setHours(0, 0, 0, 0);
    const tomorrow = new Date(today);
    tomorrow.setDate(today.getDate() + 1);
    
    const todaySessions = await ContentSession.find({
      userId: childUser._id,
      createdAt: { $gte: today, $lt: tomorrow }
    });
    
    const todayScreenTime = todaySessions.reduce((total, session) => {
      return total + (session.totalTimeSpent || 0);
    }, 0);
    
    // Calculate monthly points
    const monthlyPoints = thisMonthSessions.reduce((total, session) => {
      return total + (session.pointsEarned || 0);
    }, 0);
    
    const dashboardData = {
      child: {
        id: childUser._id,
        username: childUser.username,
        currentLevel: childUser.currentLevel,
        totalPoints: childUser.totalPoints,
        weeklyPoints: childUser.weeklyPoints,
        badges: childUser.badges
      },
      goals: {
        weeklyGoal: parentAccount.weeklyGoalPoints,
        monthlyGoal: parentAccount.monthlyGoalPoints,
        screenTimeLimit: parentAccount.contentRestrictions.maxDailyScreenTime
      },
      progress: {
        weeklyProgress: (childUser.weeklyPoints / parentAccount.weeklyGoalPoints) * 100,
        monthlyProgress: (monthlyPoints / parentAccount.monthlyGoalPoints) * 100,
        todayScreenTime: Math.floor(todayScreenTime / 60), // Convert to minutes
        readingStreak
      },
      stats: {
        ...stats,
        contentTypeBreakdown: contentTypeBreakdown.reduce((acc, item) => {
          acc[item._id] = {
            count: item.count,
            totalTime: Math.floor(item.totalTime / 60) // Convert to minutes
          };
          return acc;
        }, {}),
        averageDailyTime: stats.totalTimeSpent > 0 ? 
          Math.floor(stats.totalTimeSpent / 60 / Math.max(1, stats.totalSessions)) : 0
      },
      restrictions: parentAccount.contentRestrictions
    };
    
    res.json({
      success: true,
      data: dashboardData
    });
    
  } catch (error) {
    console.error('Get child dashboard error:', error);
    res.status(500).json({
      error: 'Failed to get child dashboard data',
      code: 'GET_DASHBOARD_ERROR'
    });
  }
});

// Get child's recent activity
router.get('/child-activity', authenticateParent, async (req, res) => {
  try {
    const { limit = 20, filter = 'all' } = req.query;
    const childUser = req.childUser;
    
    let activities = [];
    
    if (filter === 'all' || filter === 'completed') {
      // Get recent completed sessions
      const recentSessions = await ContentSession.find({
        userId: childUser._id,
        status: 'completed'
      })
      .sort({ completedAt: -1 })
      .limit(parseInt(limit));
      
      const sessionActivities = recentSessions.map(session => ({
        type: 'session_completed',
        timestamp: session.completedAt,
        data: {
          contentTitle: session.contentTitle,
          contentType: session.contentType,
          pointsEarned: session.pointsEarned,
          timeSpent: Math.floor(session.totalTimeSpent / 60),
          chunksCompleted: session.completedChunks
        }
      }));
      
      activities = activities.concat(sessionActivities);
    }
    
    if (filter === 'all' || filter === 'achievements') {
      // Get recent badges (last 30 days)
      const thirtyDaysAgo = new Date();
      thirtyDaysAgo.setDate(thirtyDaysAgo.getDate() - 30);
      
      const recentBadges = childUser.badges.filter(badge => 
        new Date(badge.earnedAt) >= thirtyDaysAgo
      );
      
      const badgeActivities = recentBadges.map(badge => ({
        type: 'badge_earned',
        timestamp: badge.earnedAt,
        data: {
          badgeName: badge.name,
          badgeId: badge.badgeId
        }
      }));
      
      activities = activities.concat(badgeActivities);
    }
    
    // Sort all activities by timestamp
    activities.sort((a, b) => new Date(b.timestamp) - new Date(a.timestamp));
    
    // Limit results
    activities = activities.slice(0, parseInt(limit));
    
    res.json({
      success: true,
      activities: activities.map(activity => ({
        ...activity,
        timeAgo: getTimeAgo(activity.timestamp)
      }))
    });
    
  } catch (error) {
    console.error('Get child activity error:', error);
    res.status(500).json({
      error: 'Failed to get child activity',
      code: 'GET_ACTIVITY_ERROR'
    });
  }
});

// Update parent goals
router.post('/update-goals', authenticateParent, async (req, res) => {
  try {
    const { weeklyGoal, monthlyGoal, screenTimeLimit } = req.body;
    const parentAccount = req.parentAccount;
    
    // Validate goals
    if (weeklyGoal !== undefined) {
      if (weeklyGoal < 50 || weeklyGoal > 1000) {
        return res.status(400).json({
          error: 'Weekly goal must be between 50 and 1000 points',
          code: 'INVALID_WEEKLY_GOAL'
        });
      }
      parentAccount.weeklyGoalPoints = weeklyGoal;
    }
    
    if (monthlyGoal !== undefined) {
      if (monthlyGoal < 200 || monthlyGoal > 5000) {
        return res.status(400).json({
          error: 'Monthly goal must be between 200 and 5000 points',
          code: 'INVALID_MONTHLY_GOAL'
        });
      }
      parentAccount.monthlyGoalPoints = monthlyGoal;
    }
    
    if (screenTimeLimit !== undefined) {
      if (screenTimeLimit < 30 || screenTimeLimit > 300) {
        return res.status(400).json({
          error: 'Screen time limit must be between 30 and 300 minutes',
          code: 'INVALID_SCREEN_TIME'
        });
      }
      parentAccount.contentRestrictions.maxDailyScreenTime = screenTimeLimit;
    }
    
    await parentAccount.save();
    
    // Also update child's weekly goal
    if (weeklyGoal !== undefined) {
      await User.findByIdAndUpdate(req.childUser._id, { weeklyGoal });
    }
    
    res.json({
      success: true,
      message: 'Goals updated successfully',
      goals: {
        weeklyGoal: parentAccount.weeklyGoalPoints,
        monthlyGoal: parentAccount.monthlyGoalPoints,
        screenTimeLimit: parentAccount.contentRestrictions.maxDailyScreenTime
      }
    });
    
  } catch (error) {
    console.error('Update goals error:', error);
    res.status(500).json({
      error: 'Failed to update goals',
      code: 'UPDATE_GOALS_ERROR'
    });
  }
});

// Update content restrictions
router.post('/update-restrictions', authenticateParent, async (req, res) => {
  try {
    const { allowedContentTypes, blockedTopics, allowFriends } = req.body;
    const parentAccount = req.parentAccount;
    
    if (allowedContentTypes !== undefined) {
      const validTypes = ['article', 'video', 'book'];
      const filteredTypes = allowedContentTypes.filter(type => validTypes.includes(type));
      
      if (filteredTypes.length === 0) {
        return res.status(400).json({
          error: 'At least one content type must be allowed',
          code: 'NO_CONTENT_TYPES'
        });
      }
      
      parentAccount.contentRestrictions.allowedContentTypes = filteredTypes;
    }
    
    if (blockedTopics !== undefined) {
      parentAccount.contentRestrictions.blockedTopics = blockedTopics
        .filter(topic => typeof topic === 'string' && topic.trim().length > 0)
        .map(topic => topic.trim().toLowerCase())
        .slice(0, 20); // Limit to 20 blocked topics
    }
    
    if (allowFriends !== undefined) {
      parentAccount.contentRestrictions.allowFriends = Boolean(allowFriends);
    }
    
    await parentAccount.save();
    
    res.json({
      success: true,
      message: 'Content restrictions updated successfully',
      restrictions: parentAccount.contentRestrictions
    });
    
  } catch (error) {
    console.error('Update restrictions error:', error);
    res.status(500).json({
      error: 'Failed to update content restrictions',
      code: 'UPDATE_RESTRICTIONS_ERROR'
    });
  }
});

// Get progress charts data
router.get('/progress-charts', authenticateParent, async (req, res) => {
  try {
    const { period = 'week' } = req.query;
    const childUser = req.childUser;
    
    let startDate, endDate, groupBy;
    
    if (period === 'week') {
      startDate = getWeekStart();
      endDate = new Date(startDate);
      endDate.setDate(startDate.getDate() + 7);
      groupBy = { $dayOfYear: '$completedAt' };
    } else if (period === 'month') {
      startDate = new Date();
      startDate.setDate(1);
      startDate.setHours(0, 0, 0, 0);
      endDate = new Date(startDate);
      endDate.setMonth(endDate.getMonth() + 1);
      groupBy = { $dayOfMonth: '$completedAt' };
    } else {
      return res.status(400).json({
        error: 'Invalid period. Use "week" or "month"',
        code: 'INVALID_PERIOD'
      });
    }
    
    // Get points and time data
    const chartData = await ContentSession.aggregate([
      {
        $match: {
          userId: childUser._id,
          status: 'completed',
          completedAt: { $gte: startDate, $lt: endDate }
        }
      },
      {
        $group: {
          _id: groupBy,
          totalPoints: { $sum: '$pointsEarned' },
          totalTime: { $sum: '$totalTimeSpent' },
          sessionCount: { $sum: 1 }
        }
      },
      { $sort: { '_id': 1 } }
    ]);
    
    res.json({
      success: true,
      period,
      data: chartData.map(item => ({
        day: item._id,
        points: item.totalPoints,
        timeMinutes: Math.floor(item.totalTime / 60),
        sessions: item.sessionCount
      }))
    });
    
  } catch (error) {
    console.error('Get progress charts error:', error);
    res.status(500).json({
      error: 'Failed to get progress charts data',
      code: 'GET_CHARTS_ERROR'
    });
  }
});

// Helper function to get week start (Monday)
function getWeekStart() {
  const now = new Date();
  const dayOfWeek = now.getDay();
  const daysToMonday = dayOfWeek === 0 ? 6 : dayOfWeek - 1;
  
  const monday = new Date(now);
  monday.setDate(now.getDate() - daysToMonday);
  monday.setHours(0, 0, 0, 0);
  
  return monday;
}

// Helper function to calculate reading streak
async function calculateReadingStreak(userId) {
  try {
    const today = new Date();
    let currentDate = new Date(today);
    currentDate.setHours(0, 0, 0, 0);
    
    let streak = 0;
    
    for (let i = 0; i < 30; i++) {
      const dayStart = new Date(currentDate);
      const dayEnd = new Date(currentDate);
      dayEnd.setDate(dayEnd.getDate() + 1);
      
      const sessionsThisDay = await ContentSession.countDocuments({
        userId,
        status: 'completed',
        completedAt: { $gte: dayStart, $lt: dayEnd }
      });
      
      if (sessionsThisDay > 0) {
        streak++;
        currentDate.setDate(currentDate.getDate() - 1);
      } else {
        break;
      }
    }
    
    return streak;
  } catch (error) {
    console.error('Calculate streak error:', error);
    return 0;
  }
}

// Helper function to get time ago string
function getTimeAgo(date) {
  const now = new Date();
  const diffInSeconds = Math.floor((now - date) / 1000);
  
  if (diffInSeconds < 60) {
    return 'just now';
  } else if (diffInSeconds < 3600) {
    const minutes = Math.floor(diffInSeconds / 60);
    return `${minutes} minute${minutes > 1 ? 's' : ''} ago`;
  } else if (diffInSeconds < 86400) {
    const hours = Math.floor(diffInSeconds / 3600);
    return `${hours} hour${hours > 1 ? 's' : ''} ago`;
  } else {
    const days = Math.floor(diffInSeconds / 86400);
    return `${days} day${days > 1 ? 's' : ''} ago`;
  }
}

// Get activity feed with filter
router.get('/activity', authenticateParent, async (req, res) => {
  try {
    const { filter = 'all' } = req.query;
    const childUser = req.childUser;

    console.log(`📋 Getting activity feed for child ${childUser.username} with filter: ${filter}`);

    // Get recent sessions
    const recentSessions = await ContentSession.find({
      userId: childUser._id,
      status: 'completed'
    })
    .sort({ completedAt: -1 })
    .limit(10);

    const activities = recentSessions.map(session => ({
      type: 'reading',
      title: `Completed: ${session.contentTitle}`,
      description: `Earned ${session.pointsEarned} points in ${Math.floor(session.totalTimeSpent / 60)} minutes`,
      timestamp: session.completedAt
    }));

    res.json({
      success: true,
      data: activities
    });

  } catch (error) {
    console.error('Get activity error:', error);
    res.status(500).json({
      error: 'Failed to get activity data',
      code: 'GET_ACTIVITY_ERROR'
    });
  }
});

// Get charts data
router.get('/charts', authenticateParent, async (req, res) => {
  try {
    const { period = 'week' } = req.query;
    const childUser = req.childUser;

    console.log(`📊 Getting charts data for period: ${period}`);

    // For now, return placeholder data
    const chartData = {
      period,
      pointsData: [10, 20, 15, 30, 25, 40, 35],
      timeData: [30, 45, 25, 60, 40, 75, 50],
      labels: ['Mon', 'Tue', 'Wed', 'Thu', 'Fri', 'Sat', 'Sun']
    };

    res.json({
      success: true,
      data: chartData
    });

  } catch (error) {
    console.error('Get charts error:', error);
    res.status(500).json({
      error: 'Failed to get charts data',
      code: 'GET_CHARTS_ERROR'
    });
  }
});

// Update goals
router.put('/update-goals', authenticateParent, async (req, res) => {
  try {
    const { weeklyGoalPoints, monthlyGoalPoints, maxDailyScreenTime } = req.body;
    const parentAccount = req.parentAccount;

    console.log('💾 Updating parent goals:', { weeklyGoalPoints, monthlyGoalPoints, maxDailyScreenTime });

    parentAccount.weeklyGoalPoints = weeklyGoalPoints;
    parentAccount.monthlyGoalPoints = monthlyGoalPoints;
    parentAccount.contentRestrictions.maxDailyScreenTime = maxDailyScreenTime;

    await parentAccount.save();

    res.json({
      success: true,
      message: 'Goals updated successfully'
    });

  } catch (error) {
    console.error('Update goals error:', error);
    res.status(500).json({
      error: 'Failed to update goals',
      code: 'UPDATE_GOALS_ERROR'
    });
  }
});

// Update restrictions
router.put('/update-restrictions', authenticateParent, async (req, res) => {
  try {
    const { allowedContentTypes, blockedTopics, allowFriends } = req.body;
    const parentAccount = req.parentAccount;

    console.log('🔒 Updating content restrictions:', { allowedContentTypes, blockedTopics, allowFriends });

    parentAccount.contentRestrictions.allowedContentTypes = allowedContentTypes;
    parentAccount.contentRestrictions.blockedTopics = blockedTopics;
    parentAccount.contentRestrictions.allowFriends = allowFriends;

    await parentAccount.save();

    res.json({
      success: true,
      restrictions: parentAccount.contentRestrictions
    });

  } catch (error) {
    console.error('Update restrictions error:', error);
    res.status(500).json({
      error: 'Failed to update restrictions',
      code: 'UPDATE_RESTRICTIONS_ERROR'
    });
  }
});

module.exports = router;
