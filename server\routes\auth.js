const express = require('express');
const validator = require('validator');
const { User, ParentAccount } = require('../models');
const { generateUserToken, generateParentToken, authenticateToken, authenticateParent } = require('../middleware/auth');

const router = express.Router();

// User Registration
router.post('/register', async (req, res) => {
  try {
    const { username, password, dateOfBirth, interests = [] } = req.body;
    
    // Validation
    if (!username || !password || !dateOfBirth) {
      return res.status(400).json({ 
        error: 'Username, password, and date of birth are required',
        code: 'MISSING_FIELDS'
      });
    }
    
    // Validate username format
    if (!validator.isAlphanumeric(username.replace(/_/g, '')) || username.length < 3 || username.length > 20) {
      return res.status(400).json({ 
        error: 'Username must be 3-20 characters long and contain only letters, numbers, and underscores',
        code: 'INVALID_USERNAME'
      });
    }
    
    // Validate password strength
    if (password.length < 6) {
      return res.status(400).json({ 
        error: 'Password must be at least 6 characters long',
        code: 'WEAK_PASSWORD'
      });
    }
    
    // Validate date of birth and calculate age
    const birthDate = new Date(dateOfBirth);
    const age = Math.floor((Date.now() - birthDate.getTime()) / (365.25 * 24 * 60 * 60 * 1000));
    
    if (age < 6 || age > 16) {
      return res.status(400).json({ 
        error: 'Age must be between 6 and 16 years old',
        code: 'INVALID_AGE'
      });
    }
    
    // Calculate initial chunk size level based on age
    const initialChunkLevel = Math.max(1, Math.floor(age / 3));
    
    // Check if username already exists
    const existingUser = await User.findOne({ username: username.toLowerCase() });
    if (existingUser) {
      return res.status(409).json({ 
        error: 'Username already exists',
        code: 'USERNAME_EXISTS'
      });
    }
    
    // Create new user
    const user = new User({
      username: username.toLowerCase(),
      password,
      dateOfBirth: birthDate,
      chunkSizeLevel: initialChunkLevel,
      interests: interests.filter(interest => 
        ['science', 'history', 'sports', 'animals', 'technology', 'art', 'music', 'nature', 'space', 'adventure'].includes(interest)
      )
    });
    
    await user.save();
    
    // Generate token
    const token = generateUserToken(user._id);
    
    // Return user data (without password)
    const userData = {
      id: user._id,
      username: user.username,
      age,
      currentLevel: user.currentLevel,
      chunkSizeLevel: user.chunkSizeLevel,
      totalPoints: user.totalPoints,
      weeklyPoints: user.weeklyPoints,
      interests: user.interests,
      profileCustomizations: user.profileCustomizations,
      badges: user.badges
    };
    
    res.status(201).json({
      message: 'User registered successfully',
      user: userData,
      token
    });
    
  } catch (error) {
    console.error('Registration error:', error);
    
    if (error.name === 'ValidationError') {
      const validationErrors = Object.values(error.errors).map(err => err.message);
      return res.status(400).json({ 
        error: 'Validation failed',
        details: validationErrors,
        code: 'VALIDATION_ERROR'
      });
    }
    
    res.status(500).json({ 
      error: 'Registration failed',
      code: 'REGISTRATION_ERROR'
    });
  }
});

// User Login
router.post('/login', async (req, res) => {
  try {
    const { username, password } = req.body;
    
    if (!username || !password) {
      return res.status(400).json({ 
        error: 'Username and password are required',
        code: 'MISSING_CREDENTIALS'
      });
    }
    
    // Find user by username (case insensitive)
    const user = await User.findOne({ username: username.toLowerCase() });
    if (!user) {
      return res.status(401).json({ 
        error: 'Invalid username or password',
        code: 'INVALID_CREDENTIALS'
      });
    }
    
    // Check password
    const isPasswordValid = await user.comparePassword(password);
    if (!isPasswordValid) {
      return res.status(401).json({ 
        error: 'Invalid username or password',
        code: 'INVALID_CREDENTIALS'
      });
    }
    
    // Update last active timestamp
    user.lastActive = new Date();
    await user.save();
    
    // Generate token
    const token = generateUserToken(user._id);
    
    // Calculate age
    const age = Math.floor((Date.now() - user.dateOfBirth.getTime()) / (365.25 * 24 * 60 * 60 * 1000));
    
    // Check if user has parent account
    const parentAccount = await ParentAccount.findOne({ childUserId: user._id });
    
    // Return user data
    const userData = {
      id: user._id,
      username: user.username,
      age,
      currentLevel: user.currentLevel,
      chunkSizeLevel: user.chunkSizeLevel,
      totalPoints: user.totalPoints,
      weeklyPoints: user.weeklyPoints,
      interests: user.interests,
      profileCustomizations: user.profileCustomizations,
      badges: user.badges,
      weeklyGoal: user.weeklyGoal,
      hasParentAccount: !!parentAccount
    };
    
    res.json({
      message: 'Login successful',
      user: userData,
      token
    });
    
  } catch (error) {
    console.error('Login error:', error);
    res.status(500).json({ 
      error: 'Login failed',
      code: 'LOGIN_ERROR'
    });
  }
});

// Parent Account Setup
router.post('/parent-setup', authenticateToken, async (req, res) => {
  try {
    const { parentPassword, parentEmail, weeklyGoal, monthlyGoal, contentRestrictions } = req.body;
    
    if (!parentPassword || !parentEmail) {
      return res.status(400).json({ 
        error: 'Parent password and email are required',
        code: 'MISSING_PARENT_FIELDS'
      });
    }
    
    // Validate parent email
    if (!validator.isEmail(parentEmail)) {
      return res.status(400).json({ 
        error: 'Please provide a valid email address',
        code: 'INVALID_EMAIL'
      });
    }
    
    // Validate parent password strength
    if (parentPassword.length < 8) {
      return res.status(400).json({ 
        error: 'Parent password must be at least 8 characters long',
        code: 'WEAK_PARENT_PASSWORD'
      });
    }
    
    // Check if parent account already exists for this child
    const existingParentAccount = await ParentAccount.findOne({ childUserId: req.userId });
    if (existingParentAccount) {
      return res.status(409).json({ 
        error: 'Parent account already exists for this child',
        code: 'PARENT_ACCOUNT_EXISTS'
      });
    }
    
    // Check if parent email is already used
    const existingEmail = await ParentAccount.findOne({ parentEmail: parentEmail.toLowerCase() });
    if (existingEmail) {
      return res.status(409).json({ 
        error: 'Parent email already in use',
        code: 'PARENT_EMAIL_EXISTS'
      });
    }
    
    // Create parent account
    const parentAccount = new ParentAccount({
      childUserId: req.userId,
      parentPassword,
      parentEmail: parentEmail.toLowerCase(),
      weeklyGoalPoints: weeklyGoal || 100,
      monthlyGoalPoints: monthlyGoal || 400,
      contentRestrictions: {
        allowedContentTypes: contentRestrictions?.allowedContentTypes || ['article', 'video', 'book'],
        blockedTopics: contentRestrictions?.blockedTopics || [],
        maxDailyScreenTime: contentRestrictions?.maxDailyScreenTime || 120,
        allowFriends: contentRestrictions?.allowFriends !== false
      }
    });
    
    await parentAccount.save();
    
    // Update child's weekly goal
    if (weeklyGoal) {
      await User.findByIdAndUpdate(req.userId, { weeklyGoal });
    }
    
    res.status(201).json({
      message: 'Parent account created successfully',
      parentAccount: {
        id: parentAccount._id,
        parentEmail: parentAccount.parentEmail,
        weeklyGoalPoints: parentAccount.weeklyGoalPoints,
        monthlyGoalPoints: parentAccount.monthlyGoalPoints,
        contentRestrictions: parentAccount.contentRestrictions
      }
    });
    
  } catch (error) {
    console.error('Parent setup error:', error);
    
    if (error.name === 'ValidationError') {
      const validationErrors = Object.values(error.errors).map(err => err.message);
      return res.status(400).json({ 
        error: 'Validation failed',
        details: validationErrors,
        code: 'VALIDATION_ERROR'
      });
    }
    
    res.status(500).json({ 
      error: 'Parent account setup failed',
      code: 'PARENT_SETUP_ERROR'
    });
  }
});

// Parent Login
router.post('/parent-login', async (req, res) => {
  try {
    const { parentEmail, parentPassword } = req.body;
    
    if (!parentEmail || !parentPassword) {
      return res.status(400).json({ 
        error: 'Parent email and password are required',
        code: 'MISSING_PARENT_CREDENTIALS'
      });
    }
    
    // Find parent account by email
    const parentAccount = await ParentAccount.findOne({ 
      parentEmail: parentEmail.toLowerCase() 
    }).populate('childUserId', '-password');
    
    if (!parentAccount) {
      return res.status(401).json({ 
        error: 'Invalid parent email or password',
        code: 'INVALID_PARENT_CREDENTIALS'
      });
    }
    
    // Check password
    const isPasswordValid = await parentAccount.comparePassword(parentPassword);
    if (!isPasswordValid) {
      return res.status(401).json({ 
        error: 'Invalid parent email or password',
        code: 'INVALID_PARENT_CREDENTIALS'
      });
    }
    
    // Update last login
    parentAccount.lastLogin = new Date();
    await parentAccount.save();
    
    // Generate parent token
    const token = generateParentToken(parentAccount._id, parentAccount.childUserId._id);
    
    res.json({
      message: 'Parent login successful',
      parentAccount: {
        id: parentAccount._id,
        parentEmail: parentAccount.parentEmail,
        weeklyGoalPoints: parentAccount.weeklyGoalPoints,
        monthlyGoalPoints: parentAccount.monthlyGoalPoints,
        contentRestrictions: parentAccount.contentRestrictions,
        notifications: parentAccount.notifications
      },
      childUser: {
        id: parentAccount.childUserId._id,
        username: parentAccount.childUserId.username,
        currentLevel: parentAccount.childUserId.currentLevel,
        totalPoints: parentAccount.childUserId.totalPoints,
        weeklyPoints: parentAccount.childUserId.weeklyPoints
      },
      token
    });
    
  } catch (error) {
    console.error('Parent login error:', error);
    res.status(500).json({ 
      error: 'Parent login failed',
      code: 'PARENT_LOGIN_ERROR'
    });
  }
});

// Verify Token
router.get('/verify', authenticateToken, (req, res) => {
  const age = Math.floor((Date.now() - req.user.dateOfBirth.getTime()) / (365.25 * 24 * 60 * 60 * 1000));
  
  res.json({
    valid: true,
    user: {
      id: req.user._id,
      username: req.user.username,
      age,
      currentLevel: req.user.currentLevel,
      chunkSizeLevel: req.user.chunkSizeLevel,
      totalPoints: req.user.totalPoints,
      weeklyPoints: req.user.weeklyPoints,
      interests: req.user.interests,
      profileCustomizations: req.user.profileCustomizations,
      badges: req.user.badges,
      weeklyGoal: req.user.weeklyGoal
    }
  });
});

// Verify Parent Token
router.get('/verify-parent', authenticateParent, (req, res) => {
  res.json({
    valid: true,
    parentAccount: {
      id: req.parentAccount._id,
      parentEmail: req.parentAccount.parentEmail,
      weeklyGoalPoints: req.parentAccount.weeklyGoalPoints,
      monthlyGoalPoints: req.parentAccount.monthlyGoalPoints,
      contentRestrictions: req.parentAccount.contentRestrictions,
      notifications: req.parentAccount.notifications
    },
    childUser: {
      id: req.childUser._id,
      username: req.childUser.username,
      currentLevel: req.childUser.currentLevel,
      totalPoints: req.childUser.totalPoints,
      weeklyPoints: req.childUser.weeklyPoints
    }
  });
});

module.exports = router;
