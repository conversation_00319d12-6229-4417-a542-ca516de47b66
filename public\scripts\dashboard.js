// Dashboard Manager
class DashboardManager {
  constructor() {
    this.currentUser = null;
    this.userStats = null;
  }

  async loadDashboard(user) {
    try {
      this.currentUser = user;
      
      // Load user statistics
      await this.loadUserStats();
      
      // Update dashboard displays
      this.updateStatsDisplay();
      this.updateProgressDisplay();
      
    } catch (error) {
      console.error('Dashboard loading error:', error);
      throw error;
    }
  }

  async loadUserStats() {
    try {
      const token = localStorage.getItem('brainripe_token');
      const response = await fetch('/api/progress/stats', {
        method: 'GET',
        headers: {
          'Authorization': `Bearer ${token}`
        }
      });

      if (response.ok) {
        const data = await response.json();
        this.userStats = data.stats;
      } else {
        // Use fallback stats
        this.userStats = {
          totalSessions: 0,
          completedSessions: 0,
          totalPointsEarned: this.currentUser.totalPoints || 0,
          totalTimeSpent: 0,
          totalChunksCompleted: 0,
          averageRating: 0
        };
      }
    } catch (error) {
      console.error('Load user stats error:', error);
      // Use fallback stats
      this.userStats = {
        totalSessions: 0,
        completedSessions: 0,
        totalPointsEarned: this.currentUser.totalPoints || 0,
        totalTimeSpent: 0,
        totalChunksCompleted: 0,
        averageRating: 0
      };
    }
  }

  updateStatsDisplay() {
    if (!this.userStats) return;

    // Update quick stats cards
    const totalSessionsEl = document.getElementById('total-sessions');
    if (totalSessionsEl) {
      totalSessionsEl.textContent = this.userStats.completedSessions || 0;
    }

    const totalTimeEl = document.getElementById('total-time');
    if (totalTimeEl) {
      const totalMinutes = Math.floor((this.userStats.totalTimeSpent || 0) / 60);
      totalTimeEl.textContent = `${totalMinutes}m`;
    }

    const totalBadgesEl = document.getElementById('total-badges');
    if (totalBadgesEl) {
      totalBadgesEl.textContent = this.currentUser.badges?.length || 0;
    }
  }

  updateProgressDisplay() {
    if (!this.currentUser) return;

    // Update weekly progress
    const weeklyProgressEl = document.getElementById('weekly-progress');
    if (weeklyProgressEl) {
      const weeklyPoints = this.currentUser.weeklyPoints || 0;
      const weeklyGoal = this.currentUser.weeklyGoal || 100;
      weeklyProgressEl.textContent = `${weeklyPoints}/${weeklyGoal}`;
    }

    // Update level progress (if you have a level progress bar)
    this.updateLevelProgress();
  }

  updateLevelProgress() {
    const currentLevel = this.currentUser.currentLevel || 1;
    const totalPoints = this.currentUser.totalPoints || 0;
    
    // Calculate progress to next level (every 500 points)
    const pointsForCurrentLevel = (currentLevel - 1) * 500;
    const pointsForNextLevel = currentLevel * 500;
    const progressInCurrentLevel = totalPoints - pointsForCurrentLevel;
    const progressPercentage = (progressInCurrentLevel / 500) * 100;

    // Update level progress bar if it exists
    const levelProgressBar = document.getElementById('level-progress-bar');
    if (levelProgressBar) {
      levelProgressBar.style.width = `${Math.min(progressPercentage, 100)}%`;
    }

    const levelProgressText = document.getElementById('level-progress-text');
    if (levelProgressText) {
      const pointsToNext = pointsForNextLevel - totalPoints;
      levelProgressText.textContent = `${pointsToNext} points to level ${currentLevel + 1}`;
    }
  }

  // Show user profile modal
  showProfile() {
    const modal = this.createProfileModal();
    document.body.appendChild(modal);
    modal.classList.remove('hidden');
  }

  createProfileModal() {
    const modal = document.createElement('div');
    modal.className = 'modal profile-modal';
    modal.innerHTML = `
      <div class="modal-content">
        <div class="modal-header">
          <h3>Your Profile</h3>
          <button class="modal-close">×</button>
        </div>
        <div class="modal-body">
          <div class="profile-section">
            <h4>Account Info</h4>
            <div class="profile-info">
              <div class="info-item">
                <label>Username:</label>
                <span>${this.currentUser.username}</span>
              </div>
              <div class="info-item">
                <label>Level:</label>
                <span>${this.currentUser.currentLevel}</span>
              </div>
              <div class="info-item">
                <label>Total Points:</label>
                <span>${this.currentUser.totalPoints}</span>
              </div>
              <div class="info-item">
                <label>Weekly Points:</label>
                <span>${this.currentUser.weeklyPoints}</span>
              </div>
            </div>
          </div>
          
          <div class="profile-section">
            <h4>Interests</h4>
            <div class="interests-display">
              ${this.currentUser.interests?.map(interest => 
                `<span class="interest-tag">${interest}</span>`
              ).join('') || '<span class="no-interests">No interests selected</span>'}
            </div>
          </div>
          
          <div class="profile-section">
            <h4>Badges (${this.currentUser.badges?.length || 0})</h4>
            <div class="badges-display">
              ${this.currentUser.badges?.map(badge => 
                `<div class="badge-item">
                  <span class="badge-icon">🏅</span>
                  <span class="badge-name">${badge.name}</span>
                </div>`
              ).join('') || '<span class="no-badges">No badges earned yet</span>'}
            </div>
          </div>
          
          <div class="profile-section">
            <h4>Customization</h4>
            <div class="customization-options">
              <div class="custom-option">
                <label>Theme:</label>
                <select id="theme-select">
                  <option value="default" ${this.currentUser.profileCustomizations?.theme === 'default' ? 'selected' : ''}>Default</option>
                  <option value="ocean" ${this.currentUser.profileCustomizations?.theme === 'ocean' ? 'selected' : ''}>Ocean</option>
                  <option value="forest" ${this.currentUser.profileCustomizations?.theme === 'forest' ? 'selected' : ''}>Forest</option>
                  <option value="space" ${this.currentUser.profileCustomizations?.theme === 'space' ? 'selected' : ''}>Space</option>
                </select>
              </div>
            </div>
          </div>
        </div>
        <div class="modal-footer">
          <button class="btn btn-secondary" id="close-profile">Close</button>
          <button class="btn btn-primary" id="save-profile">Save Changes</button>
        </div>
      </div>
    `;

    // Add event listeners
    modal.querySelector('.modal-close').addEventListener('click', () => {
      modal.remove();
    });

    modal.querySelector('#close-profile').addEventListener('click', () => {
      modal.remove();
    });

    modal.querySelector('#save-profile').addEventListener('click', () => {
      this.saveProfileChanges(modal);
    });

    return modal;
  }

  async saveProfileChanges(modal) {
    try {
      const theme = modal.querySelector('#theme-select').value;
      
      // Update theme
      await this.updateUserTheme(theme);
      
      // Apply theme immediately
      this.applyTheme(theme);
      
      // Update current user data
      this.currentUser.profileCustomizations.theme = theme;
      
      // Show success message
      window.brainRipeApp.showNotification('Profile updated!', 'success');
      
      // Close modal
      modal.remove();
      
    } catch (error) {
      console.error('Save profile error:', error);
      window.brainRipeApp.showNotification('Failed to save changes', 'error');
    }
  }

  async updateUserTheme(theme) {
    try {
      const token = localStorage.getItem('brainripe_token');
      const response = await fetch('/api/user/update-theme', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${token}`
        },
        body: JSON.stringify({ theme })
      });

      if (!response.ok) {
        throw new Error('Failed to update theme');
      }
    } catch (error) {
      console.error('Update theme error:', error);
      throw error;
    }
  }

  applyTheme(theme) {
    // Remove existing theme classes
    document.body.classList.remove('theme-default', 'theme-ocean', 'theme-forest', 'theme-space');
    
    // Add new theme class
    document.body.classList.add(`theme-${theme}`);
    
    // Store theme preference
    localStorage.setItem('brainripe_theme', theme);
  }

  // Initialize theme on page load
  initializeTheme() {
    const savedTheme = localStorage.getItem('brainripe_theme') || 
                      this.currentUser?.profileCustomizations?.theme || 
                      'default';
    this.applyTheme(savedTheme);
  }

  // Show achievements/badges modal
  showAchievements() {
    const modal = this.createAchievementsModal();
    document.body.appendChild(modal);
    modal.classList.remove('hidden');
  }

  createAchievementsModal() {
    const modal = document.createElement('div');
    modal.className = 'modal achievements-modal';
    
    const badges = this.currentUser.badges || [];
    const availableBadges = this.getAvailableBadges();
    
    modal.innerHTML = `
      <div class="modal-content">
        <div class="modal-header">
          <h3>Your Achievements</h3>
          <button class="modal-close">×</button>
        </div>
        <div class="modal-body">
          <div class="achievements-section">
            <h4>Earned Badges (${badges.length})</h4>
            <div class="badges-grid">
              ${badges.map(badge => `
                <div class="badge-card earned">
                  <div class="badge-icon">🏅</div>
                  <div class="badge-name">${badge.name}</div>
                  <div class="badge-date">Earned: ${new Date(badge.earnedAt).toLocaleDateString()}</div>
                </div>
              `).join('') || '<p class="no-badges">No badges earned yet. Keep reading to earn your first badge!</p>'}
            </div>
          </div>
          
          <div class="achievements-section">
            <h4>Available Badges</h4>
            <div class="badges-grid">
              ${availableBadges.map(badge => `
                <div class="badge-card available">
                  <div class="badge-icon">🔒</div>
                  <div class="badge-name">${badge.name}</div>
                  <div class="badge-requirement">${badge.requirement}</div>
                </div>
              `).join('')}
            </div>
          </div>
        </div>
        <div class="modal-footer">
          <button class="btn btn-primary" id="close-achievements">Close</button>
        </div>
      </div>
    `;

    // Add event listeners
    modal.querySelector('.modal-close').addEventListener('click', () => {
      modal.remove();
    });

    modal.querySelector('#close-achievements').addEventListener('click', () => {
      modal.remove();
    });

    return modal;
  }

  getAvailableBadges() {
    const earnedBadgeIds = (this.currentUser.badges || []).map(b => b.badgeId);
    
    const allBadges = [
      { id: 'first_completion', name: 'First Steps', requirement: 'Complete your first reading session' },
      { id: 'news_reader', name: 'News Explorer', requirement: 'Complete 5 news articles' },
      { id: 'video_watcher', name: 'Video Scholar', requirement: 'Complete 5 educational videos' },
      { id: 'book_reader', name: 'Book Lover', requirement: 'Complete 5 books or stories' },
      { id: 'point_collector_100', name: 'Point Collector', requirement: 'Earn 100 total points' },
      { id: 'point_collector_500', name: 'Point Master', requirement: 'Earn 500 total points' },
      { id: 'point_collector_1000', name: 'Point Champion', requirement: 'Earn 1000 total points' },
      { id: 'streak_reader', name: 'Consistent Reader', requirement: 'Read for 7 days in a row' },
      { id: 'speed_reader', name: 'Speed Reader', requirement: 'Complete 10 sessions quickly' },
      { id: 'curious_mind', name: 'Curious Mind', requirement: 'Try all content types' }
    ];

    return allBadges.filter(badge => !earnedBadgeIds.includes(badge.id));
  }

  // Update user stats in real-time
  updateUserData(newUserData) {
    this.currentUser = { ...this.currentUser, ...newUserData };
    this.updateStatsDisplay();
    this.updateProgressDisplay();
  }
}
