# BrainRipe 🧠🍎

A gamified web application that motivates children (ages 8-14) to engage with long-form educational content through chunked reading/viewing, progress tracking, and reward systems.

## Features

- **AI-Powered Content Curation**: Personalized educational content recommendations
- **Chunked Learning**: Age-appropriate content segmentation for better comprehension
- **Gamification**: Points, badges, and rewards system
- **Social Features**: Friend system and weekly leaderboards
- **Parent Dashboard**: Progress monitoring and goal setting
- **Responsive Design**: Works on desktop and mobile devices

## Technology Stack

- **Frontend**: HTML5, CSS3, JavaScript (ES6+)
- **Backend**: Node.js with Express
- **Database**: MongoDB
- **AI Integration**: OpenAI API for content curation
- **Authentication**: JWT tokens

## Quick Start

1. **Clone the repository**
   ```bash
   git clone <repository-url>
   cd brainripe
   ```

2. **Install dependencies**
   ```bash
   npm install
   ```

3. **Set up environment variables**
   ```bash
   cp .env.example .env
   # Edit .env with your actual values
   ```

4. **Start MongoDB**
   ```bash
   # Make sure MongoDB is running on your system
   mongod
   ```

5. **Run the application**
   ```bash
   # Development mode
   npm run dev
   
   # Production mode
   npm start
   ```

6. **Open your browser**
   Navigate to `http://localhost:3000`

## Project Structure

```
brainripe/
├── public/                 # Frontend files
│   ├── index.html         # Main HTML file
│   ├── styles/            # CSS files
│   ├── scripts/           # JavaScript files
│   └── images/            # Static images
├── server/                # Backend files
│   ├── server.js          # Main server file
│   ├── models/            # Database models
│   ├── routes/            # API routes
│   ├── middleware/        # Custom middleware
│   └── utils/             # Utility functions
├── tests/                 # Test files
├── package.json           # Dependencies and scripts
└── README.md             # This file
```

## API Endpoints

### Authentication
- `POST /api/auth/register` - User registration
- `POST /api/auth/login` - User login
- `POST /api/auth/parent-setup` - Parent account setup

### Content
- `GET /api/content/curate` - Get curated content
- `POST /api/content/start-session` - Start reading session
- `POST /api/content/complete-chunk/:sessionId` - Complete chunk

### Progress
- `GET /api/progress/stats` - Get user statistics
- `POST /api/progress/add-points` - Add points to user

### Social
- `GET /api/social/leaderboard` - Weekly leaderboard
- `POST /api/social/friend-request` - Send friend request
- `POST /api/social/accept-friend/:requesterId` - Accept friend request

## Contributing

1. Fork the repository
2. Create a feature branch
3. Make your changes
4. Add tests for new functionality
5. Submit a pull request

## License

MIT License - see LICENSE file for details
