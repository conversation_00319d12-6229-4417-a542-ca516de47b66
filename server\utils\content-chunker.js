class ContentChunker {
  constructor() {
    this.sentenceEndRegex = /[.!?]+\s+/g;
    this.paragraphRegex = /\n\s*\n/g;
  }

  // Main method to chunk content based on type and user level
  async chunkContent(contentData, userChunkLevel, contentType) {
    try {
      const chunkSize = this.calculateChunkSize(userChunkLevel, contentType);
      
      switch (contentType) {
        case 'article':
          return await this.chunkArticle(contentData, chunkSize);
        case 'video':
          return await this.chunkVideo(contentData, chunkSize);
        case 'book':
          return await this.chunkBook(contentData, chunkSize);
        default:
          throw new Error(`Unsupported content type: ${contentType}`);
      }
    } catch (error) {
      console.error('Content chunking error:', error);
      return this.createFallbackChunks(contentData, contentType);
    }
  }

  // Calculate appropriate chunk size based on user level and content type
  calculateChunkSize(userChunkLevel, contentType) {
    const baseSizes = {
      article: { min: 50, max: 200, unit: 'words' },
      video: { min: 2, max: 8, unit: 'minutes' },
      book: { min: 100, max: 500, unit: 'words' }
    };

    const base = baseSizes[contentType];
    if (!base) {
      throw new Error(`Unknown content type: ${contentType}`);
    }

    // Scale chunk size based on user level (1-10)
    const levelMultiplier = Math.min(userChunkLevel * 0.3 + 0.7, 3); // 0.7 to 3.0
    
    return {
      min: Math.floor(base.min * levelMultiplier),
      max: Math.floor(base.max * levelMultiplier),
      target: Math.floor((base.min + base.max) / 2 * levelMultiplier),
      unit: base.unit
    };
  }

  // Chunk article content
  async chunkArticle(articleData, chunkSize) {
    const { title, content, url } = articleData;
    
    if (!content || typeof content !== 'string') {
      return this.createFallbackChunks(articleData, 'article');
    }

    // Clean and prepare text
    const cleanText = this.cleanText(content);
    const sentences = this.splitIntoSentences(cleanText);
    
    if (sentences.length === 0) {
      return this.createFallbackChunks(articleData, 'article');
    }

    const chunks = [];
    let currentChunk = '';
    let currentWordCount = 0;
    let chunkIndex = 0;

    for (let i = 0; i < sentences.length; i++) {
      const sentence = sentences[i].trim();
      const sentenceWordCount = this.countWords(sentence);

      // Check if adding this sentence would exceed the target chunk size
      if (currentWordCount > 0 && 
          currentWordCount + sentenceWordCount > chunkSize.target &&
          currentWordCount >= chunkSize.min) {
        
        // Finalize current chunk
        chunks.push(this.createChunk(currentChunk.trim(), chunkIndex, 'article', {
          wordCount: currentWordCount,
          estimatedReadingTime: Math.ceil(currentWordCount / 200) // 200 words per minute
        }));
        
        // Start new chunk
        currentChunk = sentence;
        currentWordCount = sentenceWordCount;
        chunkIndex++;
      } else {
        // Add sentence to current chunk
        currentChunk += (currentChunk ? ' ' : '') + sentence;
        currentWordCount += sentenceWordCount;
      }
    }

    // Add final chunk if it has content
    if (currentChunk.trim()) {
      chunks.push(this.createChunk(currentChunk.trim(), chunkIndex, 'article', {
        wordCount: currentWordCount,
        estimatedReadingTime: Math.ceil(currentWordCount / 200)
      }));
    }

    return {
      title,
      url,
      type: 'article',
      totalChunks: chunks.length,
      chunks,
      chunkSize,
      createdAt: new Date()
    };
  }

  // Chunk video content (time-based)
  async chunkVideo(videoData, chunkSize) {
    const { title, url, estimatedDuration } = videoData;
    
    if (!estimatedDuration || estimatedDuration <= 0) {
      return this.createFallbackChunks(videoData, 'video');
    }

    const targetChunkDuration = chunkSize.target; // in minutes
    const numChunks = Math.max(1, Math.ceil(estimatedDuration / targetChunkDuration));
    const actualChunkDuration = estimatedDuration / numChunks;

    const chunks = [];
    for (let i = 0; i < numChunks; i++) {
      const startTime = Math.floor(i * actualChunkDuration);
      const endTime = Math.floor((i + 1) * actualChunkDuration);
      
      chunks.push(this.createChunk(
        `Watch from ${this.formatTime(startTime)} to ${this.formatTime(endTime)}`,
        i,
        'video',
        {
          startTime,
          endTime,
          duration: actualChunkDuration,
          estimatedWatchTime: actualChunkDuration
        }
      ));
    }

    return {
      title,
      url,
      type: 'video',
      totalChunks: chunks.length,
      chunks,
      chunkSize,
      estimatedDuration,
      createdAt: new Date()
    };
  }

  // Chunk book content
  async chunkBook(bookData, chunkSize) {
    const { title, content, url } = bookData;
    
    if (!content || typeof content !== 'string') {
      return this.createFallbackChunks(bookData, 'book');
    }

    // For books, we'll chunk by paragraphs and word count
    const cleanText = this.cleanText(content);
    const paragraphs = cleanText.split(this.paragraphRegex).filter(p => p.trim());
    
    if (paragraphs.length === 0) {
      return this.createFallbackChunks(bookData, 'book');
    }

    const chunks = [];
    let currentChunk = '';
    let currentWordCount = 0;
    let chunkIndex = 0;

    for (const paragraph of paragraphs) {
      const paragraphWordCount = this.countWords(paragraph);

      // If this paragraph alone exceeds max chunk size, split it by sentences
      if (paragraphWordCount > chunkSize.max) {
        // Finalize current chunk if it has content
        if (currentChunk.trim()) {
          chunks.push(this.createChunk(currentChunk.trim(), chunkIndex, 'book', {
            wordCount: currentWordCount,
            estimatedReadingTime: Math.ceil(currentWordCount / 150) // 150 words per minute for books
          }));
          chunkIndex++;
        }

        // Split large paragraph by sentences
        const sentences = this.splitIntoSentences(paragraph);
        let tempChunk = '';
        let tempWordCount = 0;

        for (const sentence of sentences) {
          const sentenceWordCount = this.countWords(sentence);
          
          if (tempWordCount + sentenceWordCount > chunkSize.target && tempWordCount >= chunkSize.min) {
            chunks.push(this.createChunk(tempChunk.trim(), chunkIndex, 'book', {
              wordCount: tempWordCount,
              estimatedReadingTime: Math.ceil(tempWordCount / 150)
            }));
            chunkIndex++;
            tempChunk = sentence;
            tempWordCount = sentenceWordCount;
          } else {
            tempChunk += (tempChunk ? ' ' : '') + sentence;
            tempWordCount += sentenceWordCount;
          }
        }

        // Start new chunk with remaining content
        currentChunk = tempChunk;
        currentWordCount = tempWordCount;
      } else {
        // Check if adding this paragraph would exceed target chunk size
        if (currentWordCount > 0 && 
            currentWordCount + paragraphWordCount > chunkSize.target &&
            currentWordCount >= chunkSize.min) {
          
          // Finalize current chunk
          chunks.push(this.createChunk(currentChunk.trim(), chunkIndex, 'book', {
            wordCount: currentWordCount,
            estimatedReadingTime: Math.ceil(currentWordCount / 150)
          }));
          
          // Start new chunk
          currentChunk = paragraph;
          currentWordCount = paragraphWordCount;
          chunkIndex++;
        } else {
          // Add paragraph to current chunk
          currentChunk += (currentChunk ? '\n\n' : '') + paragraph;
          currentWordCount += paragraphWordCount;
        }
      }
    }

    // Add final chunk if it has content
    if (currentChunk.trim()) {
      chunks.push(this.createChunk(currentChunk.trim(), chunkIndex, 'book', {
        wordCount: currentWordCount,
        estimatedReadingTime: Math.ceil(currentWordCount / 150)
      }));
    }

    return {
      title,
      url,
      type: 'book',
      totalChunks: chunks.length,
      chunks,
      chunkSize,
      createdAt: new Date()
    };
  }

  // Helper method to create a chunk object
  createChunk(content, index, type, metadata = {}) {
    return {
      index,
      content,
      type,
      metadata,
      id: `chunk_${index}_${Date.now()}_${Math.random().toString(36).substr(2, 5)}`
    };
  }

  // Helper method to clean text
  cleanText(text) {
    return text
      .replace(/\r\n/g, '\n')
      .replace(/\r/g, '\n')
      .replace(/\t/g, ' ')
      .replace(/\s+/g, ' ')
      .trim();
  }

  // Helper method to split text into sentences
  splitIntoSentences(text) {
    return text
      .split(this.sentenceEndRegex)
      .map(sentence => sentence.trim())
      .filter(sentence => sentence.length > 0);
  }

  // Helper method to count words
  countWords(text) {
    return text.trim().split(/\s+/).filter(word => word.length > 0).length;
  }

  // Helper method to format time for videos
  formatTime(minutes) {
    const mins = Math.floor(minutes);
    const secs = Math.floor((minutes - mins) * 60);
    return `${mins}:${secs.toString().padStart(2, '0')}`;
  }

  // Create fallback chunks when chunking fails
  createFallbackChunks(contentData, contentType) {
    const { title, url } = contentData;
    
    const fallbackContent = {
      article: "This is an interesting article waiting for you to explore. Click the link to start reading!",
      video: "This is an educational video ready for you to watch. Click the link to start viewing!",
      book: "This is an engaging book or story waiting for you to discover. Click the link to start reading!"
    };

    return {
      title: title || 'Educational Content',
      url: url || '#',
      type: contentType,
      totalChunks: 1,
      chunks: [
        this.createChunk(
          fallbackContent[contentType],
          0,
          contentType,
          { isFallback: true }
        )
      ],
      chunkSize: { min: 1, max: 1, target: 1, unit: 'fallback' },
      createdAt: new Date(),
      isFallback: true
    };
  }

  // Method to re-chunk content with different parameters
  async rechunkContent(existingChunkedContent, newChunkLevel) {
    const { title, url, type } = existingChunkedContent;
    
    // Reconstruct original content from chunks
    const originalContent = existingChunkedContent.chunks
      .map(chunk => chunk.content)
      .join(type === 'book' ? '\n\n' : ' ');

    // Re-chunk with new parameters
    return this.chunkContent(
      { title, url, content: originalContent },
      newChunkLevel,
      type
    );
  }
}

module.exports = ContentChunker;
