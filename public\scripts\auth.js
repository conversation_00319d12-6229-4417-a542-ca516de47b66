// Authentication Manager
class AuthManager {
  constructor() {
    this.baseUrl = '/api/auth';
  }

  async login(username, password) {
    try {
      const response = await fetch(`${this.baseUrl}/login`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json'
        },
        body: JSON.stringify({ username, password })
      });

      const data = await response.json();
      
      if (response.ok) {
        return {
          success: true,
          user: data.user,
          token: data.token
        };
      } else {
        return {
          success: false,
          error: data.error || 'Login failed'
        };
      }
    } catch (error) {
      console.error('Login request error:', error);
      return {
        success: false,
        error: 'Network error. Please check your connection.'
      };
    }
  }

  async register(username, password, dateOfBirth, interests = []) {
    try {
      const response = await fetch(`${this.baseUrl}/register`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json'
        },
        body: JSON.stringify({
          username,
          password,
          dateOfBirth,
          interests
        })
      });

      const data = await response.json();
      
      if (response.ok) {
        return {
          success: true,
          user: data.user,
          token: data.token
        };
      } else {
        return {
          success: false,
          error: data.error || 'Registration failed'
        };
      }
    } catch (error) {
      console.error('Registration request error:', error);
      return {
        success: false,
        error: 'Network error. Please check your connection.'
      };
    }
  }

  async verifyToken(token) {
    try {
      const response = await fetch(`${this.baseUrl}/verify`, {
        method: 'GET',
        headers: {
          'Authorization': `Bearer ${token}`
        }
      });

      if (response.ok) {
        const data = await response.json();
        return data;
      } else {
        return null;
      }
    } catch (error) {
      console.error('Token verification error:', error);
      return null;
    }
  }

  async setupParentAccount(parentPassword, parentEmail, weeklyGoal, monthlyGoal, contentRestrictions) {
    try {
      const token = localStorage.getItem('brainripe_token');
      const response = await fetch(`${this.baseUrl}/parent-setup`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${token}`
        },
        body: JSON.stringify({
          parentPassword,
          parentEmail,
          weeklyGoal,
          monthlyGoal,
          contentRestrictions
        })
      });

      const data = await response.json();
      
      if (response.ok) {
        return {
          success: true,
          parentAccount: data.parentAccount
        };
      } else {
        return {
          success: false,
          error: data.error || 'Parent account setup failed'
        };
      }
    } catch (error) {
      console.error('Parent setup error:', error);
      return {
        success: false,
        error: 'Network error. Please check your connection.'
      };
    }
  }

  async parentLogin(parentEmail, parentPassword) {
    try {
      const response = await fetch(`${this.baseUrl}/parent-login`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json'
        },
        body: JSON.stringify({
          parentEmail,
          parentPassword
        })
      });

      const data = await response.json();
      
      if (response.ok) {
        return {
          success: true,
          parentAccount: data.parentAccount,
          childUser: data.childUser,
          token: data.token
        };
      } else {
        return {
          success: false,
          error: data.error || 'Parent login failed'
        };
      }
    } catch (error) {
      console.error('Parent login error:', error);
      return {
        success: false,
        error: 'Network error. Please check your connection.'
      };
    }
  }
}
