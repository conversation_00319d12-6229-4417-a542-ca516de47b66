// Test setup and configuration
const mongoose = require('mongoose');

// Set test environment
process.env.NODE_ENV = 'test';
process.env.JWT_SECRET = 'test-jwt-secret-key';
process.env.MONGODB_TEST_URI = 'mongodb://localhost:27017/brainripe_test';

// Increase timeout for tests
jest.setTimeout(30000);

// Global test setup
beforeAll(async () => {
  // Suppress console logs during tests unless debugging
  if (!process.env.DEBUG_TESTS) {
    console.log = jest.fn();
    console.error = jest.fn();
    console.warn = jest.fn();
  }
});

// Global test teardown
afterAll(async () => {
  // Close any remaining connections
  if (mongoose.connection.readyState !== 0) {
    await mongoose.connection.close();
  }
});

// Mock external services for testing
jest.mock('../server/utils/ai-integration', () => {
  return class MockContentCurator {
    async getCuratedContent(userId, age, interests, forceRefresh = false) {
      return {
        articles: [
          {
            id: 'test-article-1',
            title: 'Test Science Article',
            description: 'A test article about science',
            url: 'https://example.com/science-article',
            topics: ['science', 'technology'],
            difficulty: 5,
            estimatedReadTime: 8
          }
        ],
        videos: [
          {
            id: 'test-video-1',
            title: 'Test Educational Video',
            description: 'A test educational video',
            url: 'https://example.com/educational-video',
            topics: ['education', 'learning'],
            difficulty: 4,
            estimatedWatchTime: 12
          }
        ],
        books: [
          {
            id: 'test-book-1',
            title: 'Test Children Book',
            description: 'A test book for children',
            url: 'https://example.com/children-book',
            topics: ['adventure', 'friendship'],
            difficulty: 6,
            estimatedReadTime: 20
          }
        ],
        generatedAt: new Date(),
        userAge: age
      };
    }

    async refreshContentCache(interests, level) {
      return true;
    }
  };
});

jest.mock('../server/utils/content-chunker', () => {
  return class MockContentChunker {
    async chunkContent(content, chunkSizeLevel, contentType) {
      const baseChunks = {
        article: 3,
        video: 2,
        book: 5
      };

      const numChunks = baseChunks[contentType] || 3;
      const chunks = [];

      for (let i = 0; i < numChunks; i++) {
        chunks.push({
          index: i,
          content: `Chunk ${i + 1} of ${content.title}: ${content.content || 'Sample content'}`,
          type: contentType,
          metadata: {
            estimatedTime: contentType === 'video' ? 5 : 3,
            difficulty: Math.max(3, Math.min(8, chunkSizeLevel + 2))
          },
          id: `chunk_${i}_${Date.now()}`
        });
      }

      return {
        title: content.title,
        url: content.url,
        type: contentType,
        totalChunks: numChunks,
        chunks
      };
    }
  };
});

// Mock email service for parent account verification
jest.mock('nodemailer', () => ({
  createTransport: jest.fn(() => ({
    sendMail: jest.fn((options, callback) => {
      if (callback) {
        callback(null, { messageId: 'test-message-id' });
      }
      return Promise.resolve({ messageId: 'test-message-id' });
    })
  }))
}));

// Helper functions for tests
global.createTestUser = async (overrides = {}) => {
  const { User } = require('../server/models');
  
  const defaultUser = {
    username: 'testuser',
    password: 'password123',
    dateOfBirth: new Date('2010-01-01'),
    interests: ['science', 'technology']
  };

  const userData = { ...defaultUser, ...overrides };
  const user = new User(userData);
  await user.save();
  
  return user;
};

global.createTestSession = async (userId, overrides = {}) => {
  const { ContentSession } = require('../server/models');
  
  const defaultSession = {
    userId,
    contentUrl: 'https://example.com/test-content',
    contentTitle: 'Test Content',
    contentType: 'article',
    totalChunks: 3,
    completedChunks: 0,
    status: 'active'
  };

  const sessionData = { ...defaultSession, ...overrides };
  const session = new ContentSession(sessionData);
  await session.save();
  
  return session;
};

global.getAuthToken = async (user) => {
  const request = require('supertest');
  const app = require('../server/server');
  
  const response = await request(app)
    .post('/api/auth/login')
    .send({
      username: user.username,
      password: 'password123'
    });
    
  return response.body.token;
};

// Database helpers
global.clearDatabase = async () => {
  const collections = mongoose.connection.collections;
  
  for (const key in collections) {
    const collection = collections[key];
    await collection.deleteMany({});
  }
};

global.closeDatabase = async () => {
  await mongoose.connection.dropDatabase();
  await mongoose.connection.close();
};

// Test data generators
global.generateTestContent = (type = 'article', overrides = {}) => {
  const baseContent = {
    article: {
      title: 'Test Article',
      description: 'A test article for learning',
      url: 'https://example.com/test-article',
      topics: ['science', 'education'],
      difficulty: 5,
      estimatedReadTime: 8
    },
    video: {
      title: 'Test Video',
      description: 'A test educational video',
      url: 'https://example.com/test-video',
      topics: ['technology', 'learning'],
      difficulty: 4,
      estimatedWatchTime: 12
    },
    book: {
      title: 'Test Book',
      description: 'A test book for children',
      url: 'https://example.com/test-book',
      topics: ['adventure', 'friendship'],
      difficulty: 6,
      estimatedReadTime: 25
    }
  };

  return { ...baseContent[type], ...overrides };
};

// Custom matchers
expect.extend({
  toBeValidObjectId(received) {
    const pass = mongoose.Types.ObjectId.isValid(received);
    if (pass) {
      return {
        message: () => `expected ${received} not to be a valid ObjectId`,
        pass: true,
      };
    } else {
      return {
        message: () => `expected ${received} to be a valid ObjectId`,
        pass: false,
      };
    }
  },

  toHaveValidJWT(received) {
    const jwt = require('jsonwebtoken');
    try {
      jwt.verify(received, process.env.JWT_SECRET);
      return {
        message: () => `expected ${received} not to be a valid JWT`,
        pass: true,
      };
    } catch (error) {
      return {
        message: () => `expected ${received} to be a valid JWT, but got error: ${error.message}`,
        pass: false,
      };
    }
  }
});

module.exports = {
  createTestUser: global.createTestUser,
  createTestSession: global.createTestSession,
  getAuthToken: global.getAuthToken,
  clearDatabase: global.clearDatabase,
  closeDatabase: global.closeDatabase,
  generateTestContent: global.generateTestContent
};
