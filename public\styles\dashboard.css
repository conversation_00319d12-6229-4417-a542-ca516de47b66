/* Dashboard Styles */

/* Authentication Screen */
#auth-screen {
  display: flex;
  align-items: center;
  justify-content: center;
  min-height: 100vh;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  padding: 2rem;
}

.auth-container {
  background: white;
  border-radius: 30px;
  padding: 3rem;
  box-shadow: 0 20px 60px rgba(0, 0, 0, 0.2);
  max-width: 450px;
  width: 100%;
  text-align: center;
}

.auth-header {
  margin-bottom: 2rem;
}

.logo {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 0.5rem;
  margin-bottom: 1rem;
}

.brain-icon, .apple-icon {
  font-size: 2.5rem;
  animation: float 3s ease-in-out infinite;
}

.apple-icon {
  animation-delay: 0.5s;
}

@keyframes float {
  0%, 100% { transform: translateY(0px); }
  50% { transform: translateY(-10px); }
}

.tagline {
  color: #718096;
  font-size: 1.1rem;
  margin: 0;
}

.auth-form {
  text-align: left;
}

.auth-form h2 {
  text-align: center;
  margin-bottom: 2rem;
  color: #2d3748;
}

.auth-switch {
  text-align: center;
  margin-top: 1.5rem;
  color: #718096;
}

.auth-switch a {
  color: #4facfe;
  text-decoration: none;
  font-weight: 600;
}

.auth-switch a:hover {
  text-decoration: underline;
}

.interests-grid {
  display: grid;
  grid-template-columns: repeat(2, 1fr);
  gap: 0.75rem;
  margin-top: 0.5rem;
}

.interest-item {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  padding: 0.5rem;
  border-radius: 10px;
  cursor: pointer;
  transition: all 0.3s ease;
  font-size: 0.9rem;
}

.interest-item:hover {
  background: #f7fafc;
}

.interest-item input[type="checkbox"] {
  width: auto;
  margin: 0;
}

/* Main Dashboard */
#dashboard-screen {
  background: linear-gradient(135deg, #f093fb 0%, #f5576c 100%);
  min-height: 100vh;
}

.main-header {
  background: white;
  padding: 1rem 2rem;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.1);
  display: flex;
  align-items: center;
  justify-content: space-between;
  position: sticky;
  top: 0;
  z-index: 100;
}

.header-left {
  display: flex;
  align-items: center;
  gap: 1rem;
}

.logo-small {
  font-size: 1.5rem;
}

.header-left h1 {
  font-size: 1.5rem;
  margin: 0;
  color: #2d3748;
}

.header-center {
  flex: 1;
  display: flex;
  justify-content: center;
}

.user-stats {
  display: flex;
  gap: 2rem;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  padding: 0.75rem 2rem;
  border-radius: 25px;
  color: white;
}

.stat-item {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  font-weight: 600;
}

.stat-icon {
  font-size: 1.2rem;
}

.header-right {
  display: flex;
  gap: 0.5rem;
}

/* Dashboard Main Content */
.dashboard-main {
  padding: 2rem;
  max-width: 1200px;
  margin: 0 auto;
}

/* Content Hub */
.hub-container {
  background: white;
  border-radius: 25px;
  padding: 2rem;
  margin-bottom: 2rem;
  box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
}

.hub-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 2rem;
}

.hub-header h2 {
  margin: 0;
  color: #2d3748;
}

.refresh-content {
  display: flex;
  align-items: center;
  gap: 0.5rem;
}

.content-categories {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
  gap: 1.5rem;
  margin-bottom: 2rem;
}

.category-card {
  background: linear-gradient(135deg, #a8edea 0%, #fed6e3 100%);
  border-radius: 20px;
  padding: 1.5rem;
  text-align: center;
  cursor: pointer;
  transition: all 0.3s ease;
  border: 3px solid transparent;
}

.category-card:hover {
  transform: translateY(-5px);
  box-shadow: 0 15px 40px rgba(0, 0, 0, 0.15);
}

.category-card.selected {
  border-color: #4facfe;
  transform: translateY(-5px);
}

.category-icon {
  font-size: 3rem;
  margin-bottom: 1rem;
}

.category-card h3 {
  margin-bottom: 0.5rem;
  color: #2d3748;
}

.category-card p {
  color: #718096;
  margin-bottom: 1rem;
}

.category-count {
  font-size: 0.9rem;
  font-weight: 600;
  color: #4a5568;
}

/* Content Grid */
.content-grid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(300px, 1fr));
  gap: 1.5rem;
  margin-top: 2rem;
}

.content-card {
  background: white;
  border-radius: 20px;
  padding: 1.5rem;
  box-shadow: 0 8px 25px rgba(0, 0, 0, 0.1);
  transition: all 0.3s ease;
  border: 2px solid transparent;
}

.content-card:hover {
  transform: translateY(-5px);
  box-shadow: 0 15px 40px rgba(0, 0, 0, 0.15);
  border-color: #4facfe;
}

.content-card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 1rem;
}

.content-type-icon {
  font-size: 1.5rem;
}

.content-difficulty {
  background: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);
  color: white;
  padding: 0.25rem 0.75rem;
  border-radius: 15px;
  font-size: 0.8rem;
  font-weight: 600;
}

.content-title {
  font-size: 1.1rem;
  margin-bottom: 0.75rem;
  color: #2d3748;
  line-height: 1.4;
}

.content-description {
  color: #718096;
  font-size: 0.9rem;
  margin-bottom: 1rem;
  line-height: 1.5;
}

.content-meta {
  display: flex;
  gap: 1rem;
  margin-bottom: 1rem;
  font-size: 0.85rem;
  color: #a0aec0;
}

.content-topics {
  display: flex;
  flex-wrap: wrap;
  gap: 0.5rem;
  margin-bottom: 1.5rem;
}

.topic-tag {
  background: #edf2f7;
  color: #4a5568;
  padding: 0.25rem 0.75rem;
  border-radius: 12px;
  font-size: 0.8rem;
  font-weight: 500;
}

.start-content-btn {
  width: 100%;
  justify-content: space-between;
}

.points-preview {
  background: rgba(255, 255, 255, 0.2);
  padding: 0.25rem 0.5rem;
  border-radius: 10px;
  font-size: 0.85rem;
}

/* Sections */
.section {
  background: white;
  border-radius: 25px;
  padding: 2rem;
  margin-bottom: 2rem;
  box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
}

.section h3 {
  margin-bottom: 1.5rem;
  color: #2d3748;
}

/* Active Sessions */
.sessions-list {
  display: flex;
  flex-direction: column;
  gap: 1rem;
}

.session-card {
  display: flex;
  align-items: center;
  gap: 1rem;
  padding: 1rem;
  background: #f7fafc;
  border-radius: 15px;
  transition: all 0.3s ease;
}

.session-card:hover {
  background: #edf2f7;
  transform: translateX(5px);
}

.session-icon {
  font-size: 2rem;
  width: 60px;
  height: 60px;
  display: flex;
  align-items: center;
  justify-content: center;
  background: linear-gradient(135deg, #a8edea 0%, #fed6e3 100%);
  border-radius: 15px;
}

.session-info {
  flex: 1;
}

.session-info h4 {
  margin-bottom: 0.5rem;
  color: #2d3748;
}

.session-progress {
  display: flex;
  align-items: center;
  gap: 1rem;
}

.session-progress .progress-bar {
  flex: 1;
  max-width: 200px;
}

.session-progress span {
  font-size: 0.9rem;
  color: #718096;
  white-space: nowrap;
}

.continue-btn {
  padding: 0.5rem 1rem;
  font-size: 0.9rem;
}

.no-sessions {
  text-align: center;
  color: #a0aec0;
  font-style: italic;
  padding: 2rem;
}

/* Quick Stats */
.stats-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: 1.5rem;
}

.stat-card {
  text-align: center;
  padding: 1.5rem;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
  border-radius: 20px;
  transition: all 0.3s ease;
}

.stat-card:hover {
  transform: translateY(-5px);
}

.stat-card .stat-icon {
  font-size: 2.5rem;
  margin-bottom: 0.5rem;
  display: block;
}

.stat-value {
  font-size: 2rem;
  font-weight: bold;
  margin-bottom: 0.25rem;
}

.stat-label {
  font-size: 0.9rem;
  opacity: 0.9;
}

/* Responsive Design */
@media (max-width: 768px) {
  .main-header {
    padding: 1rem;
    flex-direction: column;
    gap: 1rem;
  }
  
  .header-center {
    order: -1;
  }
  
  .user-stats {
    gap: 1rem;
    padding: 0.5rem 1rem;
  }
  
  .dashboard-main {
    padding: 1rem;
  }
  
  .content-categories {
    grid-template-columns: 1fr;
  }
  
  .content-grid {
    grid-template-columns: 1fr;
  }
  
  .stats-grid {
    grid-template-columns: repeat(2, 1fr);
  }
  
  .session-card {
    flex-direction: column;
    text-align: center;
  }
  
  .session-progress {
    width: 100%;
    justify-content: center;
  }
}

@media (max-width: 480px) {
  .auth-container {
    padding: 2rem;
    margin: 1rem;
  }
  
  .interests-grid {
    grid-template-columns: 1fr;
  }
  
  .user-stats {
    flex-direction: column;
    gap: 0.5rem;
    text-align: center;
  }
  
  .stats-grid {
    grid-template-columns: 1fr;
  }
  
  .hub-header {
    flex-direction: column;
    gap: 1rem;
    text-align: center;
  }
}
