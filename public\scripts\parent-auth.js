// Parent Authentication Manager
class ParentAuthManager {
  constructor() {
    this.baseURL = '/api/auth';
  }

  // Parent login
  async parentLogin(email, password) {
    try {
      console.log('🔐 Attempting parent login for:', email);
      
      const response = await fetch(`${this.baseURL}/parent-login`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          parentEmail: email,
          parentPassword: password
        })
      });

      const data = await response.json();
      
      if (response.ok) {
        console.log('✅ Parent login successful');
        return {
          success: true,
          token: data.token,
          parentAccount: data.parentAccount,
          childUser: data.childUser
        };
      } else {
        console.error('❌ Parent login failed:', data.error);
        return {
          success: false,
          error: data.error || 'Login failed'
        };
      }
    } catch (error) {
      console.error('❌ Parent login error:', error);
      return {
        success: false,
        error: 'Network error. Please check your connection.'
      };
    }
  }

  // Verify parent token
  async verifyParentToken(token) {
    try {
      console.log('🔍 Verifying parent token...');
      
      const response = await fetch(`${this.baseURL}/verify-parent`, {
        method: 'GET',
        headers: {
          'Authorization': `Bearer ${token}`,
          'Content-Type': 'application/json'
        }
      });

      if (response.ok) {
        const data = await response.json();
        console.log('✅ Parent token verified');
        return {
          parentAccount: data.parentAccount,
          childUser: data.childUser
        };
      } else {
        console.log('❌ Parent token invalid or expired');
        return null;
      }
    } catch (error) {
      console.error('❌ Token verification error:', error);
      return null;
    }
  }

  // Create parent account
  async createParentAccount(parentData) {
    try {
      console.log('👨‍👩‍👧‍👦 Creating parent account...');
      
      const token = localStorage.getItem('brainripe_token');
      if (!token) {
        throw new Error('Child user must be logged in first');
      }

      const response = await fetch(`${this.baseURL}/create-parent-account`, {
        method: 'POST',
        headers: {
          'Authorization': `Bearer ${token}`,
          'Content-Type': 'application/json'
        },
        body: JSON.stringify(parentData)
      });

      const data = await response.json();
      
      if (response.ok) {
        console.log('✅ Parent account created successfully');
        return {
          success: true,
          parentAccount: data.parentAccount
        };
      } else {
        console.error('❌ Parent account creation failed:', data.error);
        return {
          success: false,
          error: data.error || 'Failed to create parent account'
        };
      }
    } catch (error) {
      console.error('❌ Parent account creation error:', error);
      return {
        success: false,
        error: error.message || 'Network error. Please try again.'
      };
    }
  }

  // Update parent password
  async updateParentPassword(currentPassword, newPassword) {
    try {
      console.log('🔑 Updating parent password...');
      
      const token = localStorage.getItem('brainripe_parent_token');
      if (!token) {
        throw new Error('Parent must be logged in');
      }

      const response = await fetch(`${this.baseURL}/update-parent-password`, {
        method: 'PUT',
        headers: {
          'Authorization': `Bearer ${token}`,
          'Content-Type': 'application/json'
        },
        body: JSON.stringify({
          currentPassword,
          newPassword
        })
      });

      const data = await response.json();
      
      if (response.ok) {
        console.log('✅ Parent password updated successfully');
        return {
          success: true,
          message: 'Password updated successfully'
        };
      } else {
        console.error('❌ Password update failed:', data.error);
        return {
          success: false,
          error: data.error || 'Failed to update password'
        };
      }
    } catch (error) {
      console.error('❌ Password update error:', error);
      return {
        success: false,
        error: error.message || 'Network error. Please try again.'
      };
    }
  }

  // Get parent account info
  async getParentAccountInfo() {
    try {
      console.log('📋 Getting parent account info...');
      
      const token = localStorage.getItem('brainripe_parent_token');
      if (!token) {
        throw new Error('Parent must be logged in');
      }

      const response = await fetch(`${this.baseURL}/parent-info`, {
        method: 'GET',
        headers: {
          'Authorization': `Bearer ${token}`,
          'Content-Type': 'application/json'
        }
      });

      if (response.ok) {
        const data = await response.json();
        console.log('✅ Parent account info retrieved');
        return {
          success: true,
          parentAccount: data.parentAccount,
          childUser: data.childUser
        };
      } else {
        const data = await response.json();
        console.error('❌ Failed to get parent info:', data.error);
        return {
          success: false,
          error: data.error || 'Failed to get account info'
        };
      }
    } catch (error) {
      console.error('❌ Get parent info error:', error);
      return {
        success: false,
        error: error.message || 'Network error. Please try again.'
      };
    }
  }

  // Logout parent
  logout() {
    console.log('🚪 Parent logging out...');
    localStorage.removeItem('brainripe_parent_token');
    return {
      success: true,
      message: 'Logged out successfully'
    };
  }

  // Check if parent is logged in
  isLoggedIn() {
    const token = localStorage.getItem('brainripe_parent_token');
    return !!token;
  }

  // Get stored token
  getToken() {
    return localStorage.getItem('brainripe_parent_token');
  }

  // Store token
  storeToken(token) {
    localStorage.setItem('brainripe_parent_token', token);
  }

  // Clear token
  clearToken() {
    localStorage.removeItem('brainripe_parent_token');
  }
}
