// Parent Dashboard Manager
class ParentDashboardManager {
  constructor() {
    this.baseURL = '/api/parent';
    this.currentChartPeriod = 'week';
    this.currentActivityFilter = 'all';
  }

  // Load main dashboard data
  async loadDashboard(parentAccount, childUser) {
    try {
      console.log('📊 Loading parent dashboard data...');
      
      const token = localStorage.getItem('brainripe_parent_token');
      const response = await fetch(`${this.baseURL}/child-dashboard`, {
        method: 'GET',
        headers: {
          'Authorization': `Bearer ${token}`,
          'Content-Type': 'application/json'
        }
      });

      if (response.ok) {
        const data = await response.json();
        console.log('✅ Dashboard data loaded');
        
        this.updateDashboardUI(data.data);
        await this.loadCharts(this.currentChartPeriod);
        await this.loadActivity(this.currentActivityFilter);
        
        return { success: true, data: data.data };
      } else {
        const errorData = await response.json();
        console.error('❌ Failed to load dashboard:', errorData.error);
        return { success: false, error: errorData.error };
      }
    } catch (error) {
      console.error('❌ Dashboard loading error:', error);
      return { success: false, error: 'Failed to load dashboard data' };
    }
  }

  // Update dashboard UI with data
  updateDashboardUI(data) {
    try {
      // Update child info
      const childNameEl = document.getElementById('child-name');
      const childLevelEl = document.getElementById('child-level');
      
      if (childNameEl) childNameEl.textContent = data.child.username;
      if (childLevelEl) childLevelEl.textContent = `Level ${data.child.currentLevel}`;

      // Update progress cards
      this.updateProgressCard('weekly-progress', data.progress.weeklyProgress, data.goals.weeklyGoal);
      this.updateProgressCard('monthly-progress', data.progress.monthlyProgress, data.goals.monthlyGoal);
      this.updateProgressCard('screen-time', data.progress.todayScreenTime, data.goals.screenTimeLimit);
      this.updateProgressCard('reading-streak', data.progress.readingStreak, 7);

      // Update goals
      this.updateGoalsCard(data.goals);
      
      // Update restrictions
      this.updateRestrictionsCard(data.restrictions);
      
      // Update stats
      this.updateStatsCard(data.stats);
      
      console.log('✅ Dashboard UI updated');
    } catch (error) {
      console.error('❌ Error updating dashboard UI:', error);
    }
  }

  // Update progress card
  updateProgressCard(cardId, current, target) {
    const card = document.getElementById(cardId);
    if (!card) return;

    const progressBar = card.querySelector('.progress-fill');
    const progressValue = card.querySelector('.progress-value');
    
    let percentage = 0;
    let displayValue = '';

    switch (cardId) {
      case 'weekly-progress':
        percentage = Math.min((current / target) * 100, 100);
        displayValue = `${Math.round(current)}/${target} points`;
        break;
      case 'monthly-progress':
        percentage = Math.min((current / target) * 100, 100);
        displayValue = `${Math.round(current)}/${target} points`;
        break;
      case 'screen-time':
        percentage = Math.min((current / target) * 100, 100);
        displayValue = `${current}/${target} min`;
        break;
      case 'reading-streak':
        percentage = Math.min((current / target) * 100, 100);
        displayValue = `${current} days`;
        break;
    }

    if (progressBar) {
      progressBar.style.width = `${percentage}%`;
    }
    
    if (progressValue) {
      progressValue.textContent = displayValue;
    }
  }

  // Update goals card
  updateGoalsCard(goals) {
    const weeklyGoalEl = document.getElementById('weekly-goal-display');
    const monthlyGoalEl = document.getElementById('monthly-goal-display');
    const screenTimeLimitEl = document.getElementById('screen-time-limit-display');

    if (weeklyGoalEl) weeklyGoalEl.textContent = `${goals.weeklyGoal} points`;
    if (monthlyGoalEl) monthlyGoalEl.textContent = `${goals.monthlyGoal} points`;
    if (screenTimeLimitEl) screenTimeLimitEl.textContent = `${goals.screenTimeLimit} minutes`;
  }

  // Update restrictions card
  updateRestrictionsCard(restrictions) {
    const allowedTypesEl = document.getElementById('allowed-content-types');
    const blockedTopicsEl = document.getElementById('blocked-topics-display');
    const socialFeaturesEl = document.getElementById('social-features-display');

    if (allowedTypesEl) {
      allowedTypesEl.textContent = restrictions.allowedContentTypes.join(', ');
    }
    
    if (blockedTopicsEl) {
      blockedTopicsEl.textContent = restrictions.blockedTopics.length > 0 
        ? restrictions.blockedTopics.join(', ') 
        : 'None';
    }
    
    if (socialFeaturesEl) {
      socialFeaturesEl.textContent = restrictions.allowFriends ? 'Enabled' : 'Disabled';
    }
  }

  // Update stats card
  updateStatsCard(stats) {
    const totalSessionsEl = document.getElementById('total-sessions');
    const avgDailyTimeEl = document.getElementById('avg-daily-time');
    const favoriteTypeEl = document.getElementById('favorite-content-type');

    if (totalSessionsEl) totalSessionsEl.textContent = stats.totalSessions || 0;
    if (avgDailyTimeEl) avgDailyTimeEl.textContent = `${stats.averageDailyTime || 0} min`;
    
    // Find favorite content type
    if (favoriteTypeEl && stats.contentTypeBreakdown) {
      const types = Object.entries(stats.contentTypeBreakdown);
      const favorite = types.reduce((max, [type, data]) => 
        data.count > (max.count || 0) ? { type, ...data } : max, {});
      favoriteTypeEl.textContent = favorite.type || 'None';
    }
  }

  // Load charts data
  async loadCharts(period = 'week') {
    try {
      console.log(`📈 Loading charts for period: ${period}`);
      
      const token = localStorage.getItem('brainripe_parent_token');
      const response = await fetch(`${this.baseURL}/charts?period=${period}`, {
        method: 'GET',
        headers: {
          'Authorization': `Bearer ${token}`,
          'Content-Type': 'application/json'
        }
      });

      if (response.ok) {
        const data = await response.json();
        this.renderCharts(data.data);
        console.log('✅ Charts loaded');
        return { success: true };
      } else {
        console.error('❌ Failed to load charts');
        return { success: false };
      }
    } catch (error) {
      console.error('❌ Charts loading error:', error);
      return { success: false };
    }
  }

  // Render charts (placeholder - would integrate with Chart.js or similar)
  renderCharts(chartData) {
    // This would integrate with a charting library like Chart.js
    console.log('📊 Rendering charts with data:', chartData);
    
    // For now, just update chart containers with placeholder
    const chartContainers = document.querySelectorAll('.chart-container');
    chartContainers.forEach(container => {
      if (!container.querySelector('.chart-placeholder')) {
        container.innerHTML = `
          <div class="chart-placeholder" style="
            height: 100%;
            display: flex;
            align-items: center;
            justify-content: center;
            background: #f8f9fa;
            border-radius: 10px;
            color: #6c757d;
          ">
            📊 Chart will be rendered here
          </div>
        `;
      }
    });
  }

  // Load activity feed
  async loadActivity(filter = 'all') {
    try {
      console.log(`📋 Loading activity feed with filter: ${filter}`);
      
      const token = localStorage.getItem('brainripe_parent_token');
      const response = await fetch(`${this.baseURL}/activity?filter=${filter}`, {
        method: 'GET',
        headers: {
          'Authorization': `Bearer ${token}`,
          'Content-Type': 'application/json'
        }
      });

      if (response.ok) {
        const data = await response.json();
        this.renderActivityFeed(data.data);
        console.log('✅ Activity feed loaded');
        return { success: true };
      } else {
        console.error('❌ Failed to load activity feed');
        return { success: false };
      }
    } catch (error) {
      console.error('❌ Activity loading error:', error);
      return { success: false };
    }
  }

  // Render activity feed
  renderActivityFeed(activities) {
    const feedContainer = document.getElementById('activity-feed-container');
    if (!feedContainer) return;

    if (!activities || activities.length === 0) {
      feedContainer.innerHTML = `
        <div class="no-activity">
          <p>No recent activity to display</p>
        </div>
      `;
      return;
    }

    const activityHTML = activities.map(activity => `
      <div class="activity-item">
        <div class="activity-icon" style="background: ${this.getActivityColor(activity.type)}">
          ${this.getActivityIcon(activity.type)}
        </div>
        <div class="activity-content">
          <h4 class="activity-title">${activity.title}</h4>
          <p class="activity-description">${activity.description}</p>
        </div>
        <div class="activity-time">
          ${this.formatTime(activity.timestamp)}
        </div>
      </div>
    `).join('');

    feedContainer.innerHTML = activityHTML;
  }

  // Get activity icon
  getActivityIcon(type) {
    const icons = {
      reading: '📚',
      video: '🎥',
      quiz: '🧩',
      achievement: '🏆',
      level_up: '⬆️',
      login: '🔐'
    };
    return icons[type] || '📝';
  }

  // Get activity color
  getActivityColor(type) {
    const colors = {
      reading: '#27ae60',
      video: '#e74c3c',
      quiz: '#f39c12',
      achievement: '#9b59b6',
      level_up: '#3498db',
      login: '#95a5a6'
    };
    return colors[type] || '#bdc3c7';
  }

  // Format time
  formatTime(timestamp) {
    const date = new Date(timestamp);
    const now = new Date();
    const diff = now - date;
    
    if (diff < 60000) return 'Just now';
    if (diff < 3600000) return `${Math.floor(diff / 60000)}m ago`;
    if (diff < 86400000) return `${Math.floor(diff / 3600000)}h ago`;
    return `${Math.floor(diff / 86400000)}d ago`;
  }

  // Update goals
  async updateGoals(weeklyGoal, monthlyGoal, screenTimeLimit) {
    try {
      console.log('💾 Updating goals...');
      
      const token = localStorage.getItem('brainripe_parent_token');
      const response = await fetch(`${this.baseURL}/update-goals`, {
        method: 'PUT',
        headers: {
          'Authorization': `Bearer ${token}`,
          'Content-Type': 'application/json'
        },
        body: JSON.stringify({
          weeklyGoalPoints: weeklyGoal,
          monthlyGoalPoints: monthlyGoal,
          maxDailyScreenTime: screenTimeLimit
        })
      });

      if (response.ok) {
        console.log('✅ Goals updated successfully');
        return { success: true };
      } else {
        const data = await response.json();
        console.error('❌ Failed to update goals:', data.error);
        return { success: false, error: data.error };
      }
    } catch (error) {
      console.error('❌ Update goals error:', error);
      return { success: false, error: 'Failed to update goals' };
    }
  }

  // Update restrictions
  async updateRestrictions(allowedContentTypes, blockedTopics, allowFriends) {
    try {
      console.log('🔒 Updating restrictions...');
      
      const token = localStorage.getItem('brainripe_parent_token');
      const response = await fetch(`${this.baseURL}/update-restrictions`, {
        method: 'PUT',
        headers: {
          'Authorization': `Bearer ${token}`,
          'Content-Type': 'application/json'
        },
        body: JSON.stringify({
          allowedContentTypes,
          blockedTopics,
          allowFriends
        })
      });

      if (response.ok) {
        const data = await response.json();
        console.log('✅ Restrictions updated successfully');
        return { success: true, restrictions: data.restrictions };
      } else {
        const data = await response.json();
        console.error('❌ Failed to update restrictions:', data.error);
        return { success: false, error: data.error };
      }
    } catch (error) {
      console.error('❌ Update restrictions error:', error);
      return { success: false, error: 'Failed to update restrictions' };
    }
  }
}
